import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Settings, Database, Shield, Bell } from 'lucide-react-native';

import StatusBar from '@/components/StatusBar';

export default function AdminSettingsScreen() {
  const settingsOptions = [
    {
      id: 1,
      title: 'Pengaturan Retribusi',
      description: 'Kelola kategori dan tarif retribusi',
      icon: Database,
      color: '#3B82F6',
    },
    {
      id: 2,
      title: 'Keamanan Sistem',
      description: 'Pengaturan keamanan dan akses',
      icon: Shield,
      color: '#EF4444',
    },
    {
      id: 3,
      title: 'Notifikasi',
      description: 'Pengaturan notifikasi sistem',
      icon: Bell,
      color: '#F59E0B',
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar />
      
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.headerTitle}>Pengaturan Admin</Text>
          <Text style={styles.headerSubtitle}>Konfigurasi sistem dan aplikasi</Text>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.settingsList}>
          {settingsOptions.map((option) => (
            <TouchableOpacity key={option.id} style={styles.settingCard}>
              <View style={[styles.settingIcon, { backgroundColor: `${option.color}15` }]}>
                <option.icon size={24} color={option.color} />
              </View>
              <View style={styles.settingContent}>
                <Text style={styles.settingTitle}>{option.title}</Text>
                <Text style={styles.settingDescription}>{option.description}</Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>Informasi Sistem</Text>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Versi Aplikasi:</Text>
            <Text style={styles.infoValue}>1.0.0</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Mode:</Text>
            <Text style={styles.infoValue}>Development</Text>
          </View>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Database:</Text>
            <Text style={styles.infoValue}>Mock Mode</Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  scrollView: {
    flex: 1,
  },
  settingsList: {
    padding: 16,
    gap: 12,
  },
  settingCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  settingIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#6B7280',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F2937',
  },
});
