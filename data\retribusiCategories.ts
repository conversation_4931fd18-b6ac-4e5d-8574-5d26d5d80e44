// Temporary mock data untuk retribusi categories
// Ini akan diganti dengan data dari Supabase setelah database setup

export const mockRetribusiCategories = [
  {
    id: 'cat_1',
    name: '<PERSON><PERSON>',
    description: 'Retribusi parkir kendaraan bermotor dan non-motor',
    base_tariff: 5000,
    icon: 'car',
    color: '#3B82F6',
    is_active: true,
    created_by: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cat_2',
    name: '<PERSON><PERSON>',
    description: 'Retribusi pedagang dan kios di pasar tradisional',
    base_tariff: 15000,
    icon: 'store',
    color: '#10B981',
    is_active: true,
    created_by: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cat_3',
    name: 'Terminal',
    description: 'Retribusi terminal angkutan umum',
    base_tariff: 25000,
    icon: 'bus',
    color: '#F59E0B',
    is_active: true,
    created_by: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cat_4',
    name: 'Kebersihan',
    description: 'Retribusi pelayanan kebersihan dan sampah',
    base_tariff: 10000,
    icon: 'trash',
    color: '#EF4444',
    is_active: true,
    created_by: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'cat_5',
    name: 'Perizinan',
    description: 'Retribusi pengurusan izin usaha dan dokumen',
    base_tariff: 50000,
    icon: 'file-text',
    color: '#8B5CF6',
    is_active: true,
    created_by: null,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

// Mock role assignments untuk testing
export const mockRoleRetribusiAssignments = [
  // USER role bisa akses Parkir dan Kebersihan
  {
    id: 'assign_1',
    user_role: 'USER' as const,
    retribusi_category_id: 'cat_1', // Parkir
    can_create: true,
    can_edit: false,
    can_delete: false,
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'assign_2',
    user_role: 'USER' as const,
    retribusi_category_id: 'cat_4', // Kebersihan
    can_create: true,
    can_edit: false,
    can_delete: false,
    created_at: '2024-01-01T00:00:00Z',
  },
  // SUPERVISOR bisa akses Pasar dan Terminal
  {
    id: 'assign_3',
    user_role: 'SUPERVISOR' as const,
    retribusi_category_id: 'cat_2', // Pasar
    can_create: true,
    can_edit: true,
    can_delete: false,
    created_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 'assign_4',
    user_role: 'SUPERVISOR' as const,
    retribusi_category_id: 'cat_3', // Terminal
    can_create: true,
    can_edit: true,
    can_delete: false,
    created_at: '2024-01-01T00:00:00Z',
  },
  // ADMIN bisa akses semua (tidak perlu assignment, handled di service)
];

// Helper function untuk get categories berdasarkan role
export function getCategoriesByRole(role: 'ADMIN' | 'USER' | 'SUPERVISOR') {
  if (role === 'ADMIN') {
    return mockRetribusiCategories;
  }

  const assignments = mockRoleRetribusiAssignments.filter(
    (assignment) => assignment.user_role === role && assignment.can_create
  );

  return mockRetribusiCategories.filter((category) =>
    assignments.some((assignment) => assignment.retribusi_category_id === category.id)
  );
}
