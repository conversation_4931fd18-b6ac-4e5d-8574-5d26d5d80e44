import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Plus, Search, Edit, Trash2, Users, FileText, ToggleLeft, ToggleRight } from 'lucide-react-native';

import { opdService, type OPDWithStats } from '../../../services/opdService';
import { useAuth } from '../../../contexts/AuthContext';

export default function OPDManagementScreen() {
  const { user } = useAuth();
  const [opds, setOPDs] = useState<OPDWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredOPDs, setFilteredOPDs] = useState<OPDWithStats[]>([]);

  // Check if user is admin
  const isAdmin = user?.role === 'ADMIN';

  useEffect(() => {
    if (!isAdmin) {
      Alert.alert('Access Denied', 'Anda tidak memiliki akses ke halaman ini.');
      router.back();
      return;
    }
    loadOPDs();
  }, [isAdmin]);

  useEffect(() => {
    // Filter OPDs based on search query
    if (searchQuery.trim() === '') {
      setFilteredOPDs(opds);
    } else {
      const filtered = opds.filter(opd =>
        opd.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        opd.code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        opd.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredOPDs(filtered);
    }
  }, [searchQuery, opds]);

  const loadOPDs = async () => {
    try {
      setLoading(true);
      const data = await opdService.getAllOPDs(true);
      setOPDs(data);
    } catch (error) {
      console.error('Error loading OPDs:', error);
      Alert.alert('Error', 'Gagal memuat data OPD');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadOPDs();
    setRefreshing(false);
  };

  const handleToggleStatus = async (opd: OPDWithStats) => {
    try {
      const action = opd.is_active ? 'nonaktifkan' : 'aktifkan';
      Alert.alert(
        'Konfirmasi',
        `Apakah Anda yakin ingin ${action} OPD "${opd.name}"?`,
        [
          { text: 'Batal', style: 'cancel' },
          {
            text: 'Ya',
            onPress: async () => {
              await opdService.toggleOPDStatus(opd.id);
              await loadOPDs();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error toggling OPD status:', error);
      Alert.alert('Error', 'Gagal mengubah status OPD');
    }
  };

  const handleDelete = async (opd: OPDWithStats) => {
    try {
      const hasUsers = (opd.user_count || 0) > 0;
      const hasRetribusi = (opd.retribusi_count || 0) > 0;

      let message = `Apakah Anda yakin ingin menghapus OPD "${opd.name}"?`;
      
      if (hasUsers || hasRetribusi) {
        message += '\n\nOPD ini memiliki data terkait dan akan dinonaktifkan saja.';
      }

      Alert.alert('Konfirmasi Hapus', message, [
        { text: 'Batal', style: 'cancel' },
        {
          text: 'Hapus',
          style: 'destructive',
          onPress: async () => {
            await opdService.deleteOPD(opd.id);
            await loadOPDs();
          },
        },
      ]);
    } catch (error) {
      console.error('Error deleting OPD:', error);
      Alert.alert('Error', 'Gagal menghapus OPD');
    }
  };

  const renderOPDItem = ({ item }: { item: OPDWithStats }) => (
    <View style={[styles.opdCard, !item.is_active && styles.inactiveCard]}>
      <View style={styles.opdHeader}>
        <View style={styles.opdInfo}>
          <Text style={styles.opdName}>{item.name}</Text>
          <Text style={styles.opdCode}>{item.code}</Text>
          {item.description && (
            <Text style={styles.opdDescription}>{item.description}</Text>
          )}
        </View>
        <View style={styles.statusBadge}>
          <Text style={[styles.statusText, item.is_active ? styles.activeText : styles.inactiveText]}>
            {item.is_active ? 'Aktif' : 'Nonaktif'}
          </Text>
        </View>
      </View>

      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <Users size={16} color="#6B7280" />
          <Text style={styles.statText}>{item.user_count || 0} Users</Text>
        </View>
        <View style={styles.statItem}>
          <FileText size={16} color="#6B7280" />
          <Text style={styles.statText}>{item.retribusi_count || 0} Retribusi</Text>
        </View>
      </View>

      <View style={styles.actionRow}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push(`/(admin)/opd/${item.id}`)}
        >
          <Edit size={16} color="#3B82F6" />
          <Text style={styles.actionText}>Edit</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleToggleStatus(item)}
        >
          {item.is_active ? (
            <ToggleRight size={16} color="#10B981" />
          ) : (
            <ToggleLeft size={16} color="#6B7280" />
          )}
          <Text style={styles.actionText}>
            {item.is_active ? 'Nonaktifkan' : 'Aktifkan'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDelete(item)}
        >
          <Trash2 size={16} color="#EF4444" />
          <Text style={[styles.actionText, styles.deleteText]}>Hapus</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (!isAdmin) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Manajemen OPD</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/(admin)/opd/create')}
        >
          <Plus size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Search size={20} color="#6B7280" />
        <TextInput
          style={styles.searchInput}
          placeholder="Cari OPD..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <FlatList
        data={filteredOPDs}
        renderItem={renderOPDItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {searchQuery ? 'Tidak ada OPD yang ditemukan' : 'Belum ada OPD'}
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  addButton: {
    backgroundColor: '#3B82F6',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginVertical: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#111827',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  opdCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  inactiveCard: {
    opacity: 0.6,
    borderColor: '#D1D5DB',
  },
  opdHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  opdInfo: {
    flex: 1,
  },
  opdName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  opdCode: {
    fontSize: 14,
    fontWeight: '500',
    color: '#3B82F6',
    marginBottom: 4,
  },
  opdDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: '#F3F4F6',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  activeText: {
    color: '#10B981',
  },
  inactiveText: {
    color: '#6B7280',
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  statText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 6,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  actionText: {
    fontSize: 14,
    color: '#3B82F6',
    marginLeft: 6,
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: '#FEF2F2',
  },
  deleteText: {
    color: '#EF4444',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
});
