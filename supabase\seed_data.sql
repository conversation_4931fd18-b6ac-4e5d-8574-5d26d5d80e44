-- =============================================
-- Seed Data for Report Retribusi
-- =============================================

-- =============================================
-- 1. Insert Retribusi Categories
-- =============================================
INSERT INTO public.retribusi_categories (name, description, base_tariff, icon, color) VALUES
('Parkir', 'Retribusi parkir kendaraan bermotor dan tidak bermotor', 2000.00, 'car', '#F59E0B'),
('Pasar', 'Retribusi pedagang pasar dan kios', 5000.00, 'store', '#10B981'),
('Terminal', 'Retribusi terminal angkutan umum', 3000.00, 'bus', '#6366F1'),
('Kebersihan', 'Retribusi pelayanan kebersihan', 10000.00, 'trash-2', '#EF4444'),
('Perizinan', 'Retribusi pengurusan izin usaha', 50000.00, 'file-text', '#8B5CF6'),
('Reklame', 'Retribusi pemasangan reklame', 100000.00, 'megaphone', '#F97316'),
('Air Bersih', 'Retribusi pelayanan air bersih', 15000.00, 'droplets', '#06B6D4'),
('Penerangan Jalan', 'Retribusi penerangan jalan umum', 25000.00, 'lightbulb', '#FBBF24');

-- =============================================
-- 2. Insert Role Retribusi Assignments
-- =============================================

-- USER role assignments (limited access)
INSERT INTO public.role_retribusi_assignments (user_role, retribusi_category_id, can_create, can_edit, can_delete) 
SELECT 'USER', id, true, true, false FROM public.retribusi_categories WHERE name IN ('Parkir', 'Kebersihan');

-- SUPERVISOR role assignments (more access)
INSERT INTO public.role_retribusi_assignments (user_role, retribusi_category_id, can_create, can_edit, can_delete) 
SELECT 'SUPERVISOR', id, true, true, true FROM public.retribusi_categories WHERE name IN ('Parkir', 'Pasar', 'Terminal', 'Kebersihan');

-- ADMIN role assignments (full access)
INSERT INTO public.role_retribusi_assignments (user_role, retribusi_category_id, can_create, can_edit, can_delete) 
SELECT 'ADMIN', id, true, true, true FROM public.retribusi_categories;

-- =============================================
-- 3. Create Function to Handle New User Registration
-- =============================================
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'role', 'USER')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 4. Create Trigger for New User Registration
-- =============================================
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =============================================
-- 5. Create Function for Audit Logging
-- =============================================
CREATE OR REPLACE FUNCTION public.log_audit()
RETURNS TRIGGER AS $$
DECLARE
    user_id_val UUID;
BEGIN
    -- Get current user ID
    user_id_val := auth.uid();
    
    -- Insert audit log
    INSERT INTO public.audit_logs (
        user_id,
        action,
        table_name,
        record_id,
        old_values,
        new_values
    ) VALUES (
        user_id_val,
        TG_OP,
        TG_TABLE_NAME,
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.id
            ELSE NEW.id
        END,
        CASE 
            WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD)
            WHEN TG_OP = 'UPDATE' THEN to_jsonb(OLD)
            ELSE NULL
        END,
        CASE 
            WHEN TG_OP = 'DELETE' THEN NULL
            ELSE to_jsonb(NEW)
        END
    );
    
    RETURN CASE 
        WHEN TG_OP = 'DELETE' THEN OLD
        ELSE NEW
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 6. Create Audit Triggers
-- =============================================
CREATE TRIGGER audit_users_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.log_audit();

CREATE TRIGGER audit_deposits_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.deposits
    FOR EACH ROW EXECUTE FUNCTION public.log_audit();

CREATE TRIGGER audit_retribusi_categories_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.retribusi_categories
    FOR EACH ROW EXECUTE FUNCTION public.log_audit();

-- =============================================
-- 7. Create Helper Functions
-- =============================================

-- Function to get user role
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT AS $$
BEGIN
    RETURN (SELECT role FROM public.users WHERE id = user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can access category
CREATE OR REPLACE FUNCTION public.can_user_access_category(user_id UUID, category_id UUID, action TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_role_val TEXT;
    can_access BOOLEAN := false;
BEGIN
    -- Get user role
    SELECT role INTO user_role_val FROM public.users WHERE id = user_id;
    
    -- Admin can access everything
    IF user_role_val = 'ADMIN' THEN
        RETURN true;
    END IF;
    
    -- Check specific permissions
    SELECT 
        CASE 
            WHEN action = 'create' THEN can_create
            WHEN action = 'edit' THEN can_edit
            WHEN action = 'delete' THEN can_delete
            ELSE false
        END INTO can_access
    FROM public.role_retribusi_assignments 
    WHERE user_role = user_role_val AND retribusi_category_id = category_id;
    
    RETURN COALESCE(can_access, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
