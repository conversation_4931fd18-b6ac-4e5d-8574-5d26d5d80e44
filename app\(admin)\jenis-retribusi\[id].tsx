import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  Switch,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Save, X, RefreshCw } from 'lucide-react-native';

import { jenisRetribusiService } from '../../../services/jenisRetribusiService';
import { useAuth } from '../../../contexts/AuthContext';
import type { JenisRetribusi } from '../../../lib/supabase';

interface JenisRetribusiFormData {
  name: string;
  code: string;
  base_account_number: string;
  description: string;
  is_active: boolean;
}

export default function EditJenisRetribusiScreen() {
  const { user } = useAuth();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [generatingNumber, setGeneratingNumber] = useState(false);
  const [jenisRetribusi, setJenisRetribusi] = useState<JenisRetribusi | null>(null);
  const [formData, setFormData] = useState<JenisRetribusiFormData>({
    name: '',
    code: '',
    base_account_number: '',
    description: '',
    is_active: true,
  });

  // Check if user is admin
  const isAdmin = user?.role === 'ADMIN';

  useEffect(() => {
    if (!isAdmin) {
      Alert.alert('Access Denied', 'Anda tidak memiliki akses ke halaman ini.');
      router.back();
      return;
    }

    if (!id) {
      Alert.alert('Error', 'ID jenis retribusi tidak valid');
      router.back();
      return;
    }

    loadJenisRetribusi();
  }, [isAdmin, id]);

  const loadJenisRetribusi = async () => {
    try {
      setLoading(true);
      const jenisData = await jenisRetribusiService.getJenisRetribusiById(id!);
      
      if (!jenisData) {
        Alert.alert('Error', 'Jenis retribusi tidak ditemukan');
        router.back();
        return;
      }

      setJenisRetribusi(jenisData);
      setFormData({
        name: jenisData.name || '',
        code: jenisData.code || '',
        base_account_number: jenisData.base_account_number || '',
        description: jenisData.description || '',
        is_active: jenisData.is_active,
      });
    } catch (error) {
      console.error('Error loading jenis retribusi:', error);
      Alert.alert('Error', 'Gagal memuat data jenis retribusi');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof JenisRetribusiFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const generateBaseAccountNumber = async () => {
    try {
      setGeneratingNumber(true);
      const nextNumber = await jenisRetribusiService.generateNextBaseAccountNumber();
      setFormData(prev => ({
        ...prev,
        base_account_number: nextNumber,
      }));
    } catch (error) {
      console.error('Error generating base account number:', error);
      Alert.alert('Error', 'Gagal generate nomor rekening dasar');
    } finally {
      setGeneratingNumber(false);
    }
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      Alert.alert('Error', 'Nama jenis retribusi harus diisi');
      return false;
    }

    if (!formData.code.trim()) {
      Alert.alert('Error', 'Kode jenis retribusi harus diisi');
      return false;
    }

    if (!formData.base_account_number.trim()) {
      Alert.alert('Error', 'Nomor rekening dasar harus diisi');
      return false;
    }

    // Validate base account number format
    if (!jenisRetribusiService.validateBaseAccountNumber(formData.base_account_number)) {
      Alert.alert('Error', 'Format nomor rekening dasar tidak valid. Contoh: *********');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      const updateData = {
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        base_account_number: formData.base_account_number.trim(),
        description: formData.description.trim() || null,
        is_active: formData.is_active,
      };

      await jenisRetribusiService.updateJenisRetribusi(id!, updateData);

      Alert.alert(
        'Berhasil',
        'Jenis retribusi berhasil diperbarui',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Error updating jenis retribusi:', error);
      
      let errorMessage = 'Gagal memperbarui jenis retribusi';
      if (error.message?.includes('duplicate key')) {
        if (error.message.includes('name')) {
          errorMessage = 'Nama jenis retribusi sudah digunakan';
        } else if (error.message.includes('code')) {
          errorMessage = 'Kode jenis retribusi sudah digunakan';
        } else if (error.message.includes('base_account_number')) {
          errorMessage = 'Nomor rekening dasar sudah digunakan';
        }
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setSaving(false);
    }
  };

  if (!isAdmin) {
    return null;
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={styles.loadingText}>Memuat data jenis retribusi...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => router.back()}
        >
          <X size={24} color="#6B7280" />
        </TouchableOpacity>
        <Text style={styles.title}>Edit Jenis Retribusi</Text>
        <TouchableOpacity
          style={[styles.saveButton, saving && styles.disabledButton]}
          onPress={handleSave}
          disabled={saving}
        >
          <Save size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Dasar</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nama Jenis Retribusi *</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Contoh: Jasa Umum"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Kode *</Text>
            <TextInput
              style={styles.input}
              value={formData.code}
              onChangeText={(value) => handleInputChange('code', value.toUpperCase())}
              placeholder="Contoh: JU"
              autoCapitalize="characters"
            />
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.labelRow}>
              <Text style={styles.label}>Nomor Rekening Dasar *</Text>
              <TouchableOpacity
                style={[styles.generateButton, generatingNumber && styles.disabledButton]}
                onPress={generateBaseAccountNumber}
                disabled={generatingNumber}
              >
                <RefreshCw size={16} color="#3B82F6" />
                <Text style={styles.generateText}>Generate</Text>
              </TouchableOpacity>
            </View>
            <TextInput
              style={styles.input}
              value={formData.base_account_number}
              onChangeText={(value) => handleInputChange('base_account_number', value)}
              placeholder="Contoh: *********"
            />
            <Text style={styles.helpText}>
              Format: 4.1.01.XX (XX = nomor urut 01, 02, dst)
            </Text>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Deskripsi</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              placeholder="Deskripsi jenis retribusi"
              multiline
              numberOfLines={4}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Status</Text>
          
          <View style={styles.switchRow}>
            <Text style={styles.label}>Jenis Retribusi Aktif</Text>
            <Switch
              value={formData.is_active}
              onValueChange={(value) => handleInputChange('is_active', value)}
              trackColor={{ false: '#D1D5DB', true: '#3B82F6' }}
              thumbColor={formData.is_active ? '#FFFFFF' : '#F3F4F6'}
            />
          </View>
        </View>

        <View style={styles.warningSection}>
          <Text style={styles.warningTitle}>⚠️ Peringatan</Text>
          <Text style={styles.warningText}>
            Mengubah nomor rekening dasar akan mempengaruhi semua kategori retribusi yang menggunakan jenis ini.
            Pastikan perubahan sudah sesuai dengan standar akuntansi yang berlaku.
          </Text>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  cancelButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  saveButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  labelRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  generateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  generateText: {
    fontSize: 12,
    color: '#3B82F6',
    marginLeft: 4,
    fontWeight: '500',
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
  helpText: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    fontStyle: 'italic',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  warningSection: {
    backgroundColor: '#FEF3C7',
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#F59E0B',
  },
  warningTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#92400E',
    marginBottom: 8,
  },
  warningText: {
    fontSize: 13,
    color: '#92400E',
    lineHeight: 18,
  },
  bottomPadding: {
    height: 40,
  },
});
