import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import { View, StyleSheet, Platform } from 'react-native';

export default function StatusBar() {
  // On iOS, we need to add some space for the status bar
  // On web, we don't need this
  const renderStatusBarSpace = Platform.OS === 'ios';

  return (
    <>
      <ExpoStatusBar style="dark" />
      {renderStatusBarSpace && <View style={styles.statusBarSpace} />}
    </>
  );
}

const styles = StyleSheet.create({
  statusBarSpace: {
    height: 44,
  },
});