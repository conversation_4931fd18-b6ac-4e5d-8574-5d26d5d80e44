# Backend Architecture - Report Retribusi

Rekomendasi backend dan database untuk aplikasi Report Retribusi dengan enterprise requirements.

## 🎯 Requirements Analysis

### Enterprise Needs
- **Role-based Access Control** - User vs Admin permissions
- **Data Isolation** - User hanya lihat data sendiri
- **Audit Logging** - Track semua admin actions
- **Scalability** - Support 100+ concurrent users
- **Security** - JWT authentication, data encryption
- **Real-time Updates** - Live notifications dan updates
- **File Storage** - Upload foto bukti setoran

## 🏗️ Recommended Architecture Options

### **Option 1: Supabase + PostgreSQL** ⭐ **RECOMMENDED untuk MVP**

#### 🎯 Why Supabase?
- **Fastest Development** - Backend-as-a-Service dengan setup minimal
- **Built-in Auth** - JWT authentication dengan role management
- **Row Level Security** - Perfect untuk data isolation per user
- **Real-time** - Live updates untuk dashboard
- **File Storage** - Built-in untuk foto bukti
- **Auto-generated API** - REST dan GraphQL otomatis

#### 🛠️ Tech Stack
```
Frontend: React Native + Expo
Backend: Supabase (PostgreSQL + REST API)
Database: PostgreSQL (managed by Supabase)
Auth: Supabase Auth (JWT)
Storage: Supabase Storage
Real-time: Supabase Realtime
```

#### 📊 Database Schema (PostgreSQL)
```sql
-- Users table dengan RLS (Row Level Security)
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  role user_role DEFAULT 'USER',
  department VARCHAR(100),
  position VARCHAR(100),
  phone VARCHAR(20),
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Custom enum untuk roles
CREATE TYPE user_role AS ENUM ('ADMIN', 'USER', 'SUPERVISOR');

-- Retribusi categories
CREATE TABLE retribusi_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  base_tariff DECIMAL(15,2),
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Deposits/Setoran
CREATE TABLE deposits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  category_id UUID REFERENCES retribusi_categories(id),
  title VARCHAR(255) NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  location VARCHAR(255),
  description TEXT,
  status deposit_status DEFAULT 'PENDING',
  deposit_date DATE NOT NULL,
  attachments JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE deposit_status AS ENUM ('PENDING', 'APPROVED', 'REJECTED');

-- Row Level Security Policies
ALTER TABLE deposits ENABLE ROW LEVEL SECURITY;

-- Users can only see their own deposits
CREATE POLICY "Users can view own deposits" ON deposits
  FOR SELECT USING (auth.uid() = user_id);

-- Admins can see all deposits
CREATE POLICY "Admins can view all deposits" ON deposits
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );
```

#### 🔧 Implementation Steps
```bash
# 1. Setup Supabase project
npx create-supabase-app report-retribusi

# 2. Install Supabase client
bun add @supabase/supabase-js

# 3. Configure environment
EXPO_PUBLIC_SUPABASE_URL=your-project-url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

#### 💰 Cost Estimate
- **Free Tier**: Up to 50,000 monthly active users
- **Pro Tier**: $25/month untuk production
- **Scaling**: Pay-as-you-grow model

---

### **Option 2: NestJS + PostgreSQL + Prisma** ⭐ **RECOMMENDED untuk Enterprise**

#### 🎯 Why NestJS?
- **Enterprise-Grade** - Robust architecture dengan decorators
- **TypeScript Native** - Type safety end-to-end
- **Built-in Guards** - Perfect untuk role-based access control
- **Swagger Integration** - Auto-generated API documentation
- **Microservices Ready** - Scalable architecture
- **Testing Framework** - Built-in testing utilities

#### 🛠️ Tech Stack
```
Frontend: React Native + Expo
Backend: NestJS + Express
Database: PostgreSQL + Prisma ORM
Auth: JWT + Passport.js
Storage: AWS S3 / Cloudinary
Cache: Redis (optional)
```

#### 🏗️ Project Structure
```
backend/
├── src/
│   ├── auth/
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
│   │   ├── jwt.strategy.ts
│   │   └── roles.guard.ts
│   ├── users/
│   │   ├── users.controller.ts
│   │   ├── users.service.ts
│   │   └── dto/
│   ├── deposits/
│   │   ├── deposits.controller.ts
│   │   ├── deposits.service.ts
│   │   └── dto/
│   ├── admin/
│   │   ├── admin.controller.ts
│   │   └── admin.service.ts
│   └── common/
│       ├── decorators/
│       ├── guards/
│       └── interceptors/
├── prisma/
│   ├── schema.prisma
│   └── migrations/
└── package.json
```

#### 🔧 Implementation Example
```typescript
// auth/roles.guard.ts
@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRoles = this.reflector.getAllAndOverride<Role[]>(
      ROLES_KEY,
      [context.getHandler(), context.getClass()],
    );
    
    if (!requiredRoles) return true;
    
    const { user } = context.switchToHttp().getRequest();
    return requiredRoles.some((role) => user.role === role);
  }
}

// deposits/deposits.controller.ts
@Controller('deposits')
@UseGuards(JwtAuthGuard)
export class DepositsController {
  @Get()
  @Roles(Role.USER, Role.ADMIN)
  async findAll(@Request() req) {
    // Auto-filter berdasarkan role
    return this.depositsService.findAll(req.user);
  }

  @Get('all')
  @Roles(Role.ADMIN)
  async findAllForAdmin() {
    return this.depositsService.findAllForAdmin();
  }
}
```

#### 💰 Cost Estimate
- **Development**: Higher initial setup time
- **Hosting**: $20-50/month (VPS/Cloud)
- **Database**: $20-100/month (managed PostgreSQL)
- **Storage**: $5-20/month (AWS S3)

---

### **Option 3: Next.js API Routes + Prisma + PostgreSQL** ⭐ **RECOMMENDED untuk Full-Stack**

#### 🎯 Why Next.js?
- **Same Tech Stack** - React/TypeScript consistency
- **API Routes** - Simple backend dalam same project
- **Prisma ORM** - Type-safe database access
- **Vercel Deployment** - Easy deployment dan scaling
- **Edge Functions** - Global performance

#### 🛠️ Tech Stack
```
Frontend: React Native + Expo
Backend: Next.js API Routes
Database: PostgreSQL + Prisma
Auth: NextAuth.js / Auth0
Storage: Vercel Blob / Cloudinary
Deployment: Vercel
```

#### 🏗️ Project Structure
```
backend-nextjs/
├── pages/api/
│   ├── auth/
│   │   ├── login.ts
│   │   └── register.ts
│   ├── deposits/
│   │   ├── index.ts
│   │   ├── [id].ts
│   │   └── admin/
│   ├── users/
│   └── admin/
├── lib/
│   ├── prisma.ts
│   ├── auth.ts
│   └── middleware.ts
├── prisma/
│   ├── schema.prisma
│   └── migrations/
└── package.json
```

#### 🔧 Implementation Example
```typescript
// pages/api/deposits/index.ts
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions);
  
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (req.method === 'GET') {
    const deposits = await prisma.deposit.findMany({
      where: session.user.role === 'ADMIN' 
        ? {} 
        : { userId: session.user.id },
      include: { category: true, user: true }
    });
    
    return res.json(deposits);
  }
}

// lib/middleware.ts
export function withAuth(handler: NextApiHandler, roles?: Role[]) {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    
    if (roles && !roles.includes(session.user.role)) {
      return res.status(403).json({ error: 'Forbidden' });
    }
    
    return handler(req, res);
  };
}
```

## 📊 Comparison Matrix

| Feature | Supabase | NestJS | Next.js |
|---------|----------|---------|---------|
| **Development Speed** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Enterprise Features** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Scalability** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Learning Curve** | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| **Cost (Small Scale)** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Customization** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Real-time Features** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |

## 🎯 Final Recommendation

### **For MVP & Quick Launch: Supabase**
- Fastest time to market
- Built-in enterprise features
- Perfect untuk proof of concept
- Easy migration path ke custom backend

### **For Long-term Enterprise: NestJS**
- Maximum control dan customization
- Enterprise-grade architecture
- Best untuk complex business logic
- Scalable untuk large organizations

### **For Full-Stack Simplicity: Next.js**
- Same tech stack consistency
- Good balance of features dan simplicity
- Great untuk small-medium teams
- Easy deployment dan maintenance

## 🚀 Recommended Implementation Plan

**Phase 1 (MVP)**: Start dengan **Supabase**
- Quick setup dan validation
- Built-in auth dan real-time
- Focus on frontend development

**Phase 2 (Scale)**: Migrate ke **NestJS** jika needed
- Custom business logic
- Advanced enterprise features
- Microservices architecture

Mau mulai dengan opsi mana? Saya siap membantu setup backend architecture! 🏗️
