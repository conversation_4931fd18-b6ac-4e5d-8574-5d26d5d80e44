import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, TextInput } from 'react-native';
import { router } from 'expo-router';
import { Plus, Search, Users, Edit, Trash2, UserCheck, UserX } from 'lucide-react-native';

import StatusBar from '@/components/StatusBar';

export default function UsersManagementScreen() {
  const [searchQuery, setSearchQuery] = useState('');

  // Mock users data
  const mockUsers = [
    {
      id: 'user-1',
      full_name: '<PERSON>',
      email: '<EMAIL>',
      role: 'USER',
      department: 'Dinas Pendapatan Daerah',
      position: 'Petugas Retribusi',
      is_active: true,
      created_at: '2024-01-15',
    },
    {
      id: 'user-2',
      full_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      role: 'USER',
      department: '<PERSON><PERSON>',
      position: '<PERSON><PERSON>s <PERSON>',
      is_active: true,
      created_at: '2024-01-10',
    },
    {
      id: 'user-3',
      full_name: '<PERSON><PERSON>',
      email: '<EMAIL>',
      role: 'SUPERVISOR',
      department: 'Dinas Pendapatan Daerah',
      position: 'Supervisor Retribusi',
      is_active: false,
      created_at: '2024-01-05',
    },
    {
      id: 'admin-1',
      full_name: 'Admin System',
      email: '<EMAIL>',
      role: 'ADMIN',
      department: 'IT Department',
      position: 'System Administrator',
      is_active: true,
      created_at: '2024-01-01',
    },
  ];

  const filteredUsers = mockUsers.filter(user =>
    user.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return '#EF4444';
      case 'SUPERVISOR': return '#F59E0B';
      case 'USER': return '#10B981';
      default: return '#6B7280';
    }
  };

  const getRoleBgColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return '#FEF2F2';
      case 'SUPERVISOR': return '#FEF3C7';
      case 'USER': return '#F0FDF4';
      default: return '#F9FAFB';
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar />
      
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.headerTitle}>Kelola User</Text>
          <Text style={styles.headerSubtitle}>{filteredUsers.length} user terdaftar</Text>
        </View>
        <TouchableOpacity 
          style={styles.addButton}
          onPress={() => router.push('/(admin)/users/add')}
        >
          <Plus size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      {/* Search */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Search size={20} color="#6B7280" />
          <TextInput
            style={styles.searchInput}
            placeholder="Cari user berdasarkan nama atau email..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
      </View>

      {/* Users List */}
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.usersList}>
          {filteredUsers.map((user) => (
            <View key={user.id} style={styles.userCard}>
              <View style={styles.userInfo}>
                <View style={styles.userHeader}>
                  <Text style={styles.userName}>{user.full_name}</Text>
                  <View style={styles.userActions}>
                    <TouchableOpacity 
                      style={styles.actionButton}
                      onPress={() => router.push(`/(admin)/users/${user.id}/edit`)}
                    >
                      <Edit size={16} color="#6B7280" />
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.actionButton}>
                      <Trash2 size={16} color="#EF4444" />
                    </TouchableOpacity>
                  </View>
                </View>
                
                <Text style={styles.userEmail}>{user.email}</Text>
                
                <View style={styles.userMeta}>
                  <View style={[styles.roleBadge, { backgroundColor: getRoleBgColor(user.role) }]}>
                    <Text style={[styles.roleText, { color: getRoleColor(user.role) }]}>
                      {user.role}
                    </Text>
                  </View>
                  
                  <View style={[styles.statusBadge, user.is_active ? styles.activeBadge : styles.inactiveBadge]}>
                    {user.is_active ? (
                      <UserCheck size={12} color="#10B981" />
                    ) : (
                      <UserX size={12} color="#EF4444" />
                    )}
                    <Text style={[styles.statusText, user.is_active ? styles.activeText : styles.inactiveText]}>
                      {user.is_active ? 'Aktif' : 'Nonaktif'}
                    </Text>
                  </View>
                </View>

                <View style={styles.userDetails}>
                  <Text style={styles.userDepartment}>{user.department}</Text>
                  <Text style={styles.userPosition}>{user.position}</Text>
                </View>

                <Text style={styles.userDate}>
                  Terdaftar: {new Date(user.created_at).toLocaleDateString('id-ID')}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {filteredUsers.length === 0 && (
          <View style={styles.emptyState}>
            <Users size={48} color="#D1D5DB" />
            <Text style={styles.emptyTitle}>Tidak ada user ditemukan</Text>
            <Text style={styles.emptySubtitle}>
              {searchQuery ? 'Coba ubah kata kunci pencarian' : 'Belum ada user yang terdaftar'}
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  addButton: {
    backgroundColor: '#0A2463',
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  scrollView: {
    flex: 1,
  },
  usersList: {
    padding: 16,
    gap: 12,
  },
  userCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  userInfo: {
    gap: 8,
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  userActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    padding: 8,
    borderRadius: 6,
    backgroundColor: '#F9FAFB',
  },
  userEmail: {
    fontSize: 14,
    color: '#6B7280',
  },
  userMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  roleBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '600',
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    gap: 4,
  },
  activeBadge: {
    backgroundColor: '#F0FDF4',
  },
  inactiveBadge: {
    backgroundColor: '#FEF2F2',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  activeText: {
    color: '#10B981',
  },
  inactiveText: {
    color: '#EF4444',
  },
  userDetails: {
    gap: 2,
  },
  userDepartment: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  userPosition: {
    fontSize: 13,
    color: '#6B7280',
  },
  userDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
  },
  emptySubtitle: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 8,
  },
});
