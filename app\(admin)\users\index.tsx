import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  ToggleLeft, 
  ToggleRight, 
  Shield, 
  User, 
  Crown,
  Building2,
  Key
} from 'lucide-react-native';

import { userService, type UserWithOPDs } from '../../../services/userService';
import { useAuth } from '../../../contexts/AuthContext';

export default function UserManagementScreen() {
  const { user } = useAuth();
  const [users, setUsers] = useState<UserWithOPDs[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredUsers, setFilteredUsers] = useState<UserWithOPDs[]>([]);

  // Check if user is admin
  const isAdmin = user?.role === 'ADMIN';

  useEffect(() => {
    if (!isAdmin) {
      Alert.alert('Access Denied', 'Anda tidak memiliki akses ke halaman ini.');
      router.back();
      return;
    }
    loadUsers();
  }, [isAdmin]);

  useEffect(() => {
    // Filter users based on search query
    if (searchQuery.trim() === '') {
      setFilteredUsers(users);
    } else {
      const filtered = users.filter(user =>
        user.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.nip?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.position?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.opds?.some(opd => opd.name.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setFilteredUsers(filtered);
    }
  }, [searchQuery, users]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const data = await userService.getAllUsers();
      setUsers(data);
    } catch (error) {
      console.error('Error loading users:', error);
      Alert.alert('Error', 'Gagal memuat data user');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadUsers();
    setRefreshing(false);
  };

  const handleToggleStatus = async (targetUser: UserWithOPDs) => {
    try {
      const action = targetUser.is_active ? 'nonaktifkan' : 'aktifkan';
      Alert.alert(
        'Konfirmasi',
        `Apakah Anda yakin ingin ${action} user "${targetUser.full_name}"?`,
        [
          { text: 'Batal', style: 'cancel' },
          {
            text: 'Ya',
            onPress: async () => {
              await userService.toggleUserStatus(targetUser.id);
              await loadUsers();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error toggling user status:', error);
      Alert.alert('Error', 'Gagal mengubah status user');
    }
  };

  const handleDelete = async (targetUser: UserWithOPDs) => {
    try {
      Alert.alert(
        'Konfirmasi Hapus',
        `Apakah Anda yakin ingin menghapus user "${targetUser.full_name}"?\n\nJika user memiliki data laporan, user akan dinonaktifkan saja.`,
        [
          { text: 'Batal', style: 'cancel' },
          {
            text: 'Hapus',
            style: 'destructive',
            onPress: async () => {
              await userService.deleteUser(targetUser.id);
              await loadUsers();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error deleting user:', error);
      Alert.alert('Error', 'Gagal menghapus user');
    }
  };

  const handleResetPassword = async (targetUser: UserWithOPDs) => {
    try {
      Alert.prompt(
        'Reset Password',
        `Masukkan password baru untuk "${targetUser.full_name}":`,
        [
          { text: 'Batal', style: 'cancel' },
          {
            text: 'Reset',
            onPress: async (newPassword) => {
              if (!newPassword || newPassword.length < 6) {
                Alert.alert('Error', 'Password minimal 6 karakter');
                return;
              }
              
              await userService.resetUserPassword(targetUser.id, newPassword);
              Alert.alert('Berhasil', 'Password berhasil direset');
            },
          },
        ],
        'secure-text'
      );
    } catch (error) {
      console.error('Error resetting password:', error);
      Alert.alert('Error', 'Gagal reset password');
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return <Crown size={16} color="#F59E0B" />;
      case 'SUPERVISOR':
        return <Shield size={16} color="#3B82F6" />;
      default:
        return <User size={16} color="#6B7280" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return '#F59E0B';
      case 'SUPERVISOR':
        return '#3B82F6';
      default:
        return '#6B7280';
    }
  };

  const renderUserItem = ({ item }: { item: UserWithOPDs }) => (
    <View style={[styles.userCard, !item.is_active && styles.inactiveCard]}>
      <View style={styles.userHeader}>
        <View style={styles.userInfo}>
          <Text style={styles.userName}>{item.full_name}</Text>
          <Text style={styles.userEmail}>{item.email}</Text>
          {item.nip && (
            <Text style={styles.userNip}>NIP: {item.nip}</Text>
          )}
          {item.position && (
            <Text style={styles.userPosition}>{item.position}</Text>
          )}
        </View>
        <View style={styles.statusContainer}>
          <View style={[styles.roleBadge, { backgroundColor: `${getRoleColor(item.role)}20` }]}>
            {getRoleIcon(item.role)}
            <Text style={[styles.roleText, { color: getRoleColor(item.role) }]}>
              {item.role}
            </Text>
          </View>
          <View style={styles.statusBadge}>
            <Text style={[styles.statusText, item.is_active ? styles.activeText : styles.inactiveText]}>
              {item.is_active ? 'Aktif' : 'Nonaktif'}
            </Text>
          </View>
        </View>
      </View>

      {item.opds && item.opds.length > 0 && (
        <View style={styles.opdContainer}>
          <View style={styles.opdHeader}>
            <Building2 size={14} color="#6B7280" />
            <Text style={styles.opdLabel}>OPD ({item.opds.length})</Text>
          </View>
          <View style={styles.opdList}>
            {item.opds.slice(0, 2).map((opd, index) => (
              <View key={opd.id} style={styles.opdTag}>
                <Text style={styles.opdText}>{opd.code}</Text>
              </View>
            ))}
            {item.opds.length > 2 && (
              <View style={styles.opdTag}>
                <Text style={styles.opdText}>+{item.opds.length - 2}</Text>
              </View>
            )}
          </View>
        </View>
      )}

      <View style={styles.actionRow}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push(`/(admin)/users/${item.id}`)}
        >
          <Edit size={16} color="#3B82F6" />
          <Text style={styles.actionText}>Edit</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleResetPassword(item)}
        >
          <Key size={16} color="#8B5CF6" />
          <Text style={[styles.actionText, { color: '#8B5CF6' }]}>Reset PW</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleToggleStatus(item)}
        >
          {item.is_active ? (
            <ToggleRight size={16} color="#10B981" />
          ) : (
            <ToggleLeft size={16} color="#6B7280" />
          )}
          <Text style={styles.actionText}>
            {item.is_active ? 'Nonaktif' : 'Aktif'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDelete(item)}
        >
          <Trash2 size={16} color="#EF4444" />
          <Text style={[styles.actionText, styles.deleteText]}>Hapus</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (!isAdmin) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Manajemen User</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/(admin)/users/create')}
        >
          <Plus size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Search size={20} color="#6B7280" />
        <TextInput
          style={styles.searchInput}
          placeholder="Cari user..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <FlatList
        data={filteredUsers}
        renderItem={renderUserItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {searchQuery ? 'Tidak ada user yang ditemukan' : 'Belum ada user'}
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  addButton: {
    backgroundColor: '#3B82F6',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginVertical: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#111827',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  userCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  inactiveCard: {
    opacity: 0.6,
    borderColor: '#D1D5DB',
  },
  userHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  userNip: {
    fontSize: 12,
    color: '#9CA3AF',
    marginBottom: 2,
  },
  userPosition: {
    fontSize: 14,
    color: '#374151',
    fontStyle: 'italic',
  },
  statusContainer: {
    alignItems: 'flex-end',
  },
  roleBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    marginBottom: 4,
  },
  roleText: {
    fontSize: 12,
    fontWeight: '500',
    marginLeft: 4,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: '#F3F4F6',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  activeText: {
    color: '#10B981',
  },
  inactiveText: {
    color: '#6B7280',
  },
  opdContainer: {
    marginBottom: 16,
  },
  opdHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  opdLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
    fontWeight: '500',
  },
  opdList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  opdTag: {
    backgroundColor: '#EFF6FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    marginRight: 6,
    marginBottom: 4,
  },
  opdText: {
    fontSize: 11,
    color: '#1D4ED8',
    fontWeight: '500',
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#F3F4F6',
  },
  actionText: {
    fontSize: 12,
    color: '#3B82F6',
    marginLeft: 4,
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: '#FEF2F2',
  },
  deleteText: {
    color: '#EF4444',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
});
