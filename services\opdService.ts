import { supabase } from '../lib/supabase';
import type { OPD, OPDInsert, OPDUpdate } from '../lib/supabase';

export interface OPDWithStats extends OPD {
  user_count?: number;
  retribusi_count?: number;
}

class OPDService {
  // Get all OPDs with optional stats
  async getAllOPDs(includeStats = false): Promise<OPDWithStats[]> {
    try {
      let query = supabase
        .from('opd')
        .select('*')
        .order('name');

      const { data, error } = await query;

      if (error) throw error;

      if (!includeStats) {
        return data || [];
      }

      // Get stats for each OPD
      const opdsWithStats = await Promise.all(
        (data || []).map(async (opd) => {
          // Get user count
          const { count: userCount } = await supabase
            .from('user_opd')
            .select('*', { count: 'exact', head: true })
            .eq('opd_id', opd.id);

          // Get retribusi count
          const { count: retribusiCount } = await supabase
            .from('retribusi_categories')
            .select('*', { count: 'exact', head: true })
            .eq('opd_id', opd.id);

          return {
            ...opd,
            user_count: userCount || 0,
            retribusi_count: retribusiCount || 0,
          };
        })
      );

      return opdsWithStats;
    } catch (error) {
      console.error('Error fetching OPDs:', error);
      throw error;
    }
  }

  // Get active OPDs only
  async getActiveOPDs(): Promise<OPD[]> {
    try {
      const { data, error } = await supabase
        .from('opd')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching active OPDs:', error);
      throw error;
    }
  }

  // Get OPD by ID
  async getOPDById(id: string): Promise<OPD | null> {
    try {
      const { data, error } = await supabase
        .from('opd')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching OPD:', error);
      throw error;
    }
  }

  // Create new OPD
  async createOPD(opdData: OPDInsert): Promise<OPD> {
    try {
      const { data, error } = await supabase
        .from('opd')
        .insert(opdData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating OPD:', error);
      throw error;
    }
  }

  // Update OPD
  async updateOPD(id: string, opdData: OPDUpdate): Promise<OPD> {
    try {
      const { data, error } = await supabase
        .from('opd')
        .update(opdData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating OPD:', error);
      throw error;
    }
  }

  // Delete OPD (soft delete by setting is_active = false)
  async deleteOPD(id: string): Promise<void> {
    try {
      // Check if OPD has users or retribusi categories
      const { count: userCount } = await supabase
        .from('user_opd')
        .select('*', { count: 'exact', head: true })
        .eq('opd_id', id);

      const { count: retribusiCount } = await supabase
        .from('retribusi_categories')
        .select('*', { count: 'exact', head: true })
        .eq('opd_id', id);

      if ((userCount || 0) > 0 || (retribusiCount || 0) > 0) {
        // Soft delete if has dependencies
        const { error } = await supabase
          .from('opd')
          .update({ is_active: false })
          .eq('id', id);

        if (error) throw error;
      } else {
        // Hard delete if no dependencies
        const { error } = await supabase
          .from('opd')
          .delete()
          .eq('id', id);

        if (error) throw error;
      }
    } catch (error) {
      console.error('Error deleting OPD:', error);
      throw error;
    }
  }

  // Toggle OPD active status
  async toggleOPDStatus(id: string): Promise<OPD> {
    try {
      // Get current status
      const opd = await this.getOPDById(id);
      if (!opd) throw new Error('OPD not found');

      // Toggle status
      const { data, error } = await supabase
        .from('opd')
        .update({ is_active: !opd.is_active })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error toggling OPD status:', error);
      throw error;
    }
  }

  // Get OPDs for a specific user
  async getUserOPDs(userId: string): Promise<OPD[]> {
    try {
      const { data, error } = await supabase
        .from('user_opd')
        .select(`
          opd:opd_id (
            id,
            name,
            code,
            description,
            is_active
          )
        `)
        .eq('user_id', userId);

      if (error) throw error;
      
      return (data || [])
        .map(item => item.opd)
        .filter(opd => opd && opd.is_active) as OPD[];
    } catch (error) {
      console.error('Error fetching user OPDs:', error);
      throw error;
    }
  }

  // Assign user to OPDs
  async assignUserToOPDs(userId: string, opdIds: string[]): Promise<void> {
    try {
      // Remove existing assignments
      await supabase
        .from('user_opd')
        .delete()
        .eq('user_id', userId);

      // Add new assignments
      if (opdIds.length > 0) {
        const assignments = opdIds.map(opdId => ({
          user_id: userId,
          opd_id: opdId,
        }));

        const { error } = await supabase
          .from('user_opd')
          .insert(assignments);

        if (error) throw error;
      }
    } catch (error) {
      console.error('Error assigning user to OPDs:', error);
      throw error;
    }
  }

  // Search OPDs
  async searchOPDs(query: string): Promise<OPD[]> {
    try {
      const { data, error } = await supabase
        .from('opd')
        .select('*')
        .or(`name.ilike.%${query}%,code.ilike.%${query}%,description.ilike.%${query}%`)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error searching OPDs:', error);
      throw error;
    }
  }
}

export const opdService = new OPDService();
