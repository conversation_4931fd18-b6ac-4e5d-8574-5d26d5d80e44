-- =============================================
-- Row Level Security (RLS) Policies
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.opd ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jenis_retribusi ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_opd ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.retribusi_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deposits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deposit_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_retribusi_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- =============================================
-- Users Table Policies
-- =============================================

-- Users can read their own profile
CREATE POLICY "Users can read own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile (except role)
CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (
        auth.uid() = id AND
        role = (SELECT role FROM public.users WHERE id = auth.uid())
    );

-- Admins can read all users
CREATE POLICY "Admins can read all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- Admins can insert new users
CREATE POLICY "Admins can insert users" ON public.users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- Admins can update any user
CREATE POLICY "Admins can update users" ON public.users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- =============================================
-- OPD Table Policies
-- =============================================

-- All authenticated users can read active OPDs
CREATE POLICY "Users can read active OPDs" ON public.opd
    FOR SELECT USING (
        auth.role() = 'authenticated' AND is_active = true
    );

-- Admins can manage all OPDs
CREATE POLICY "Admins can manage OPDs" ON public.opd
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- =============================================
-- Jenis Retribusi Table Policies
-- =============================================

-- All authenticated users can read active jenis retribusi
CREATE POLICY "Users can read active jenis retribusi" ON public.jenis_retribusi
    FOR SELECT USING (
        auth.role() = 'authenticated' AND is_active = true
    );

-- Admins can manage all jenis retribusi
CREATE POLICY "Admins can manage jenis retribusi" ON public.jenis_retribusi
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- =============================================
-- User-OPD Relationship Policies
-- =============================================

-- Users can read their own OPD assignments
CREATE POLICY "Users can read own OPD assignments" ON public.user_opd
    FOR SELECT USING (auth.uid() = user_id);

-- Admins can read all OPD assignments
CREATE POLICY "Admins can read all OPD assignments" ON public.user_opd
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- Admins can manage OPD assignments
CREATE POLICY "Admins can manage OPD assignments" ON public.user_opd
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- =============================================
-- Retribusi Categories Policies
-- =============================================

-- All authenticated users can read active categories
CREATE POLICY "Users can read active categories" ON public.retribusi_categories
    FOR SELECT USING (
        auth.role() = 'authenticated' AND is_active = true
    );

-- Admins can manage all categories
CREATE POLICY "Admins can manage categories" ON public.retribusi_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- =============================================
-- Deposits Table Policies
-- =============================================

-- Users can read their own deposits
CREATE POLICY "Users can read own deposits" ON public.deposits
    FOR SELECT USING (auth.uid() = user_id);

-- Users can insert their own deposits
CREATE POLICY "Users can insert own deposits" ON public.deposits
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own pending deposits
CREATE POLICY "Users can update own pending deposits" ON public.deposits
    FOR UPDATE USING (
        auth.uid() = user_id AND status = 'PENDING'
    ) WITH CHECK (
        auth.uid() = user_id AND status = 'PENDING'
    );

-- Supervisors and Admins can read all deposits
CREATE POLICY "Supervisors can read all deposits" ON public.deposits
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('ADMIN', 'SUPERVISOR')
        )
    );

-- Supervisors and Admins can approve/reject deposits
CREATE POLICY "Supervisors can approve deposits" ON public.deposits
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('ADMIN', 'SUPERVISOR')
        )
    );

-- =============================================
-- Deposit Attachments Policies
-- =============================================

-- Users can read attachments for deposits they can see
CREATE POLICY "Users can read deposit attachments" ON public.deposit_attachments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.deposits d
            WHERE d.id = deposit_id AND (
                d.user_id = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM public.users u
                    WHERE u.id = auth.uid() AND u.role IN ('ADMIN', 'SUPERVISOR')
                )
            )
        )
    );

-- Users can insert attachments for their own deposits
CREATE POLICY "Users can insert own deposit attachments" ON public.deposit_attachments
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.deposits d
            WHERE d.id = deposit_id AND d.user_id = auth.uid()
        )
    );

-- =============================================
-- Role Retribusi Assignments Policies
-- =============================================

-- All authenticated users can read role assignments
CREATE POLICY "Users can read role assignments" ON public.role_retribusi_assignments
    FOR SELECT USING (auth.role() = 'authenticated');

-- Admins can manage role assignments
CREATE POLICY "Admins can manage role assignments" ON public.role_retribusi_assignments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- =============================================
-- Audit Logs Policies
-- =============================================

-- Users can read their own audit logs
CREATE POLICY "Users can read own audit logs" ON public.audit_logs
    FOR SELECT USING (auth.uid() = user_id);

-- Admins can read all audit logs
CREATE POLICY "Admins can read all audit logs" ON public.audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- System can insert audit logs
CREATE POLICY "System can insert audit logs" ON public.audit_logs
    FOR INSERT WITH CHECK (true);
