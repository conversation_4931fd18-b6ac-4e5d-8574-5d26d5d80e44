# 🔧 Apply Database Schema - Manual Steps

## 📋 Prerequisites

1. **Supabase Project**: https://trmfcjqhfhbsesvqbnsi.supabase.co
2. **Environment**: `.env.local` configured with real credentials
3. **Development Server**: Running with real Supabase connection

## 🚀 Step-by-Step Schema Application

### **Step 1: Access Supabase Dashboard**

1. **Open**: https://supabase.com/dashboard
2. **Login** to your account
3. **Select Project**: `report-retribusi` (trmfcjqhfhbsesvqbnsi)
4. **Navigate to**: SQL Editor (left sidebar)

### **Step 2: Apply Database Schema**

1. **Click**: "New Query" in SQL Editor
2. **Copy and Paste**: Content from `supabase/schema.sql`
3. **Click**: "Run" button
4. **Wait**: For execution to complete
5. **Verify**: No errors in output

### **Step 3: Apply RLS Policies**

1. **Click**: "New Query" in SQL Editor
2. **Copy and Paste**: Content from `supabase/rls_policies.sql`
3. **Click**: "Run" button
4. **Wait**: For execution to complete
5. **Verify**: No errors in output

### **Step 4: Insert Seed Data**

1. **Click**: "New Query" in SQL Editor
2. **Copy and Paste**: Content from `supabase/seed_data.sql`
3. **Click**: "Run" button
4. **Wait**: For execution to complete
5. **Verify**: No errors in output

### **Step 5: Verify Setup**

#### **Check Tables Created**
1. **Navigate to**: Table Editor
2. **Verify these tables exist**:
   - ✅ `users`
   - ✅ `opd`
   - ✅ `jenis_retribusi`
   - ✅ `user_opd`
   - ✅ `retribusi_categories`
   - ✅ `deposits`
   - ✅ `deposit_attachments`
   - ✅ `role_retribusi_assignments`
   - ✅ `audit_logs`

#### **Check Seed Data**
1. **Open**: `opd` table
   - Should see 6 OPDs (DISPENDA, DISHUB, DLH, etc.)

2. **Open**: `jenis_retribusi` table
   - Should see 3 types (Jasa Umum, Jasa Usaha, Perizinan Tertentu)

3. **Open**: `retribusi_categories` table
   - Should see 11 retribusi categories with proper nomor_rekening

#### **Check RLS Policies**
1. **Navigate to**: Authentication > Policies
2. **Verify**: Policies exist for all tables

## 🧪 Test Database Connection

### **Test 1: Check Console Logs**

1. **Open**: http://localhost:8081
2. **Open**: Browser Developer Tools > Console
3. **Look for**: 
   ```
   Using real Supabase client
   ✅ Connected to Supabase project: trmfcjqhfhbsesvqbnsi
   ```

### **Test 2: Try User Registration**

1. **Navigate**: To admin panel (if admin user exists)
2. **Try**: Create new user
3. **Check**: User appears in Supabase Dashboard > Authentication > Users

### **Test 3: Check Data Persistence**

1. **Create**: Test deposit/report
2. **Refresh**: Browser
3. **Verify**: Data persists (not mock data)

## 🚨 Troubleshooting

### **Common Issues**

#### **"Permission denied for table"**
- **Solution**: Make sure RLS policies are applied
- **Check**: Authentication > Policies in dashboard

#### **"Table doesn't exist"**
- **Solution**: Re-run schema.sql
- **Check**: Table Editor to see which tables are missing

#### **"Function doesn't exist"**
- **Solution**: Re-run seed_data.sql
- **Check**: SQL Editor for function creation errors

#### **Still in Mock Mode**
- **Check**: `.env.local` has `EXPO_PUBLIC_FORCE_MOCK_MODE=false`
- **Restart**: Development server after env changes
- **Verify**: Console logs show "Using real Supabase client"

### **Reset Database (if needed)**

If something goes wrong and you need to start over:

1. **Navigate to**: Settings > General in Supabase Dashboard
2. **Scroll to**: "Danger Zone"
3. **Click**: "Reset database password" (this will reset the database)
4. **Re-run**: All schema files in order

## ✅ Success Indicators

After successful setup, you should see:

### **In Supabase Dashboard:**
- ✅ 9 tables created with proper structure
- ✅ RLS policies enabled on all tables
- ✅ Seed data populated (6 OPDs, 3 jenis, 11 retribusi)
- ✅ Indexes and triggers created

### **In Application:**
- ✅ Console shows "Using real Supabase client"
- ✅ User registration creates real users
- ✅ Data persists across browser refresh
- ✅ Role-based access working

### **In Browser Console:**
```
Using real Supabase client
✅ Connected to Supabase project: trmfcjqhfhbsesvqbnsi
🔐 Authentication ready
📊 Database schema loaded
```

## 🎉 Next Steps

Once schema is applied successfully:

1. **Test**: User registration and login
2. **Verify**: OPD-based retribusi filtering
3. **Check**: Admin user management
4. **Test**: Data persistence and role-based access

The database is now ready for full application testing! 🚀
