import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Calendar, Download, ChartBar as Bar<PERSON>hart3, Chart<PERSON>ie as Pie<PERSON>hart } from 'lucide-react-native';

import StatusBar from '@/components/StatusBar';
import { mockReportData } from '@/data/mockData';

export default function ReportsScreen() {
  const [activeTab, setActiveTab] = useState('harian');
  const [selectedPeriod, setSelectedPeriod] = useState('hari-ini');

  const tabOptions = [
    { id: 'harian', label: 'Harian' },
    { id: 'mingguan', label: 'Mingguan' },
    { id: 'bulanan', label: 'Bulanan' },
  ];

  const periodOptions = {
    harian: [
      { id: 'hari-ini', label: 'Hari Ini' },
      { id: 'kemarin', label: 'Kemarin' },
      { id: 'kustom', label: 'Kustom' },
    ],
    mingguan: [
      { id: 'minggu-ini', label: 'Minggu Ini' },
      { id: 'minggu-lalu', label: 'Minggu Lalu' },
      { id: 'kustom', label: 'Kustom' },
    ],
    bulanan: [
      { id: 'bulan-ini', label: 'Bulan Ini' },
      { id: 'bulan-lalu', label: 'Bulan Lalu' },
      { id: 'kustom', label: 'Kustom' },
    ],
  };

  return (
    <View style={styles.container}>
      <StatusBar />
      
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Laporan</Text>
      </View>

      <View style={styles.tabContainer}>
        {tabOptions.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tabButton,
              activeTab === tab.id && styles.activeTabButton
            ]}
            onPress={() => {
              setActiveTab(tab.id);
              setSelectedPeriod(periodOptions[tab.id as keyof typeof periodOptions][0].id);
            }}
          >
            <Text 
              style={[
                styles.tabButtonText,
                activeTab === tab.id && styles.activeTabButtonText
              ]}
            >
              {tab.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.periodContainer}>
        {periodOptions[activeTab as keyof typeof periodOptions].map((period) => (
          <TouchableOpacity
            key={period.id}
            style={[
              styles.periodButton,
              selectedPeriod === period.id && styles.activePeriodButton
            ]}
            onPress={() => setSelectedPeriod(period.id)}
          >
            <Text 
              style={[
                styles.periodButtonText,
                selectedPeriod === period.id && styles.activePeriodButtonText
              ]}
            >
              {period.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.dateContainer}>
          <Calendar size={16} color="#6B7280" />
          <Text style={styles.dateText}>{mockReportData.dateRange}</Text>
        </View>

        <View style={styles.summaryContainer}>
          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Total Setoran</Text>
            <Text style={styles.summaryValue}>Rp {mockReportData.totalAmount.toLocaleString()}</Text>
          </View>

          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Jumlah Transaksi</Text>
            <Text style={styles.summaryValue}>{mockReportData.totalTransactions}</Text>
          </View>

          <View style={styles.summaryCard}>
            <Text style={styles.summaryLabel}>Rata-rata</Text>
            <Text style={styles.summaryValue}>Rp {mockReportData.averageAmount.toLocaleString()}</Text>
          </View>
        </View>

        <View style={styles.chartContainer}>
          <View style={styles.chartHeader}>
            <Text style={styles.chartTitle}>Grafik Setoran</Text>
            <BarChart3 size={18} color="#0A2463" />
          </View>
          <View style={styles.chart}>
            <Text style={styles.chartPlaceholder}>Area grafik batang</Text>
          </View>
        </View>

        <View style={styles.chartContainer}>
          <View style={styles.chartHeader}>
            <Text style={styles.chartTitle}>Distribusi Kategori</Text>
            <PieChart size={18} color="#0A2463" />
          </View>
          <View style={styles.chart}>
            <Text style={styles.chartPlaceholder}>Area grafik pie</Text>
          </View>

          <View style={styles.categoryLegend}>
            {mockReportData.categoryDistribution.map((category) => (
              <View key={category.name} style={styles.legendItem}>
                <View 
                  style={[
                    styles.legendColor,
                    { backgroundColor: getCategoryColor(category.name) }
                  ]} 
                />
                <Text style={styles.legendText}>{category.name}</Text>
                <Text style={styles.legendValue}>{category.percentage}%</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.exportContainer}>
          <Text style={styles.exportTitle}>Ekspor Laporan</Text>
          
          <TouchableOpacity style={styles.exportButton}>
            <Download size={18} color="#FFFFFF" />
            <Text style={styles.exportButtonText}>Ekspor sebagai PDF</Text>
          </TouchableOpacity>

          <TouchableOpacity style={[styles.exportButton, styles.exportExcelButton]}>
            <Download size={18} color="#FFFFFF" />
            <Text style={styles.exportButtonText}>Ekspor sebagai Excel</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

// Helper function to get category color
const getCategoryColor = (category: string) => {
  const colorMap: Record<string, string> = {
    'Parkir': '#F59E0B',
    'Pasar': '#10B981',
    'Terminal': '#6366F1',
    'default': '#0A2463'
  };
  
  return colorMap[category] || colorMap.default;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 16,
  },
  headerTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 20,
    color: '#111827',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  tabButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: '#0A2463',
  },
  tabButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#6B7280',
  },
  activeTabButtonText: {
    color: '#0A2463',
  },
  periodContainer: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  periodButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    backgroundColor: '#F3F4F6',
    borderRadius: 16,
    marginRight: 8,
  },
  activePeriodButton: {
    backgroundColor: '#0A2463',
  },
  periodButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 12,
    color: '#6B7280',
  },
  activePeriodButtonText: {
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  dateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  dateText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#374151',
    marginLeft: 8,
  },
  summaryContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    marginRight: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  summaryValue: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#111827',
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  chartTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#111827',
  },
  chart: {
    height: 200,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 8,
    marginBottom: 16,
  },
  chartPlaceholder: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    color: '#6B7280',
  },
  categoryLegend: {
    marginTop: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    color: '#374151',
    flex: 1,
  },
  legendValue: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#111827',
  },
  exportContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  exportTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#111827',
    marginBottom: 16,
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0A2463',
    borderRadius: 8,
    paddingVertical: 12,
    marginBottom: 12,
  },
  exportExcelButton: {
    backgroundColor: '#16A34A',
  },
  exportButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#FFFFFF',
    marginLeft: 8,
  },
});