import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Calendar, MapPin, FileText, DollarSign, User, Building } from 'lucide-react-native';
import { useAuth } from '../../contexts/AuthContext';
import { DepositsService } from '../../services/depositsService';
import type { RetribusiCategory, DepositInsert } from '../../lib/supabase';

interface DepositFormProps {
  onSubmit: (deposit: DepositInsert) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
  initialData?: Partial<DepositInsert>;
}

export default function DepositForm({ 
  onSubmit, 
  onCancel, 
  loading = false,
  initialData 
}: DepositFormProps) {
  const { profile } = useAuth();
  const [categories, setCategories] = useState<RetribusiCategory[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(true);
  
  // Form state
  const [formData, setFormData] = useState({
    title: initialData?.title || '',
    category_id: initialData?.category_id || '',
    amount: initialData?.amount?.toString() || '',
    location: initialData?.location || '',
    description: initialData?.description || '',
    deposit_date: initialData?.deposit_date || new Date().toISOString().split('T')[0],
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadCategories();
  }, [profile]);

  const loadCategories = async () => {
    if (!profile) return;
    
    try {
      setLoadingCategories(true);
      const data = await DepositsService.getAvailableCategories(profile.role);
      setCategories(data);
    } catch (error) {
      console.error('Error loading categories:', error);
      Alert.alert('Error', 'Gagal memuat kategori retribusi');
    } finally {
      setLoadingCategories(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Judul setoran wajib diisi';
    }

    if (!formData.category_id) {
      newErrors.category_id = 'Kategori retribusi wajib dipilih';
    }

    if (!formData.amount.trim()) {
      newErrors.amount = 'Jumlah setoran wajib diisi';
    } else {
      const amount = parseFloat(formData.amount.replace(/[^\d]/g, ''));
      if (isNaN(amount) || amount <= 0) {
        newErrors.amount = 'Jumlah setoran harus berupa angka positif';
      }
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Lokasi setoran wajib diisi';
    }

    if (!formData.deposit_date) {
      newErrors.deposit_date = 'Tanggal setoran wajib diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm() || !profile) return;

    try {
      const amount = parseFloat(formData.amount.replace(/[^\d]/g, ''));
      
      const depositData: DepositInsert = {
        user_id: profile.id,
        title: formData.title.trim(),
        category_id: formData.category_id || null,
        amount,
        location: formData.location.trim(),
        description: formData.description.trim() || null,
        deposit_date: formData.deposit_date,
        status: 'PENDING',
      };

      await onSubmit(depositData);
    } catch (error) {
      console.error('Error submitting form:', error);
      Alert.alert('Error', 'Gagal menyimpan setoran');
    }
  };

  const formatCurrency = (value: string) => {
    const number = value.replace(/[^\d]/g, '');
    return number.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  const handleAmountChange = (value: string) => {
    const formatted = formatCurrency(value);
    setFormData(prev => ({ ...prev, amount: formatted }));
    if (errors.amount) {
      setErrors(prev => ({ ...prev, amount: '' }));
    }
  };

  if (loadingCategories) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0A2463" />
        <Text style={styles.loadingText}>Memuat form...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* User Info (Auto-populated) */}
      <View style={styles.userInfoCard}>
        <View style={styles.userInfoHeader}>
          <User size={20} color="#0A2463" />
          <Text style={styles.userInfoTitle}>Informasi Pelapor</Text>
        </View>
        <View style={styles.userInfoContent}>
          <Text style={styles.userInfoLabel}>Nama Lengkap</Text>
          <Text style={styles.userInfoValue}>{profile?.full_name}</Text>
          
          {profile?.department && (
            <>
              <Text style={styles.userInfoLabel}>Departemen</Text>
              <Text style={styles.userInfoValue}>{profile.department}</Text>
            </>
          )}
          
          {profile?.position && (
            <>
              <Text style={styles.userInfoLabel}>Jabatan</Text>
              <Text style={styles.userInfoValue}>{profile.position}</Text>
            </>
          )}
        </View>
      </View>

      {/* Form Fields */}
      <View style={styles.formSection}>
        <Text style={styles.sectionTitle}>Detail Setoran</Text>

        {/* Title */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>
            Judul Setoran <Text style={styles.required}>*</Text>
          </Text>
          <View style={[styles.inputContainer, errors.title && styles.inputError]}>
            <FileText size={20} color="#6B7280" style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Contoh: Setoran Retribusi Parkir Januari 2024"
              value={formData.title}
              onChangeText={(value) => {
                setFormData(prev => ({ ...prev, title: value }));
                if (errors.title) setErrors(prev => ({ ...prev, title: '' }));
              }}
              multiline
            />
          </View>
          {errors.title && <Text style={styles.errorText}>{errors.title}</Text>}
        </View>

        {/* Category */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>
            Kategori Retribusi <Text style={styles.required}>*</Text>
          </Text>
          <View style={[styles.inputContainer, errors.category_id && styles.inputError]}>
            <Building size={20} color="#6B7280" style={styles.inputIcon} />
            <View style={styles.pickerContainer}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryOption,
                    formData.category_id === category.id && styles.categoryOptionSelected
                  ]}
                  onPress={() => {
                    setFormData(prev => ({ ...prev, category_id: category.id }));
                    if (errors.category_id) setErrors(prev => ({ ...prev, category_id: '' }));
                  }}
                >
                  <Text style={[
                    styles.categoryOptionText,
                    formData.category_id === category.id && styles.categoryOptionTextSelected
                  ]}>
                    {category.name}
                  </Text>
                  {category.description && (
                    <Text style={styles.categoryDescription}>{category.description}</Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
          </View>
          {errors.category_id && <Text style={styles.errorText}>{errors.category_id}</Text>}
        </View>

        {/* Amount */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>
            Jumlah Setoran <Text style={styles.required}>*</Text>
          </Text>
          <View style={[styles.inputContainer, errors.amount && styles.inputError]}>
            <DollarSign size={20} color="#6B7280" style={styles.inputIcon} />
            <Text style={styles.currencyPrefix}>Rp</Text>
            <TextInput
              style={[styles.input, styles.amountInput]}
              placeholder="0"
              value={formData.amount}
              onChangeText={handleAmountChange}
              keyboardType="numeric"
            />
          </View>
          {errors.amount && <Text style={styles.errorText}>{errors.amount}</Text>}
        </View>

        {/* Location */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>
            Lokasi Setoran <Text style={styles.required}>*</Text>
          </Text>
          <View style={[styles.inputContainer, errors.location && styles.inputError]}>
            <MapPin size={20} color="#6B7280" style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="Contoh: Kantor Dinas Pendapatan Daerah"
              value={formData.location}
              onChangeText={(value) => {
                setFormData(prev => ({ ...prev, location: value }));
                if (errors.location) setErrors(prev => ({ ...prev, location: '' }));
              }}
            />
          </View>
          {errors.location && <Text style={styles.errorText}>{errors.location}</Text>}
        </View>

        {/* Date */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>
            Tanggal Setoran <Text style={styles.required}>*</Text>
          </Text>
          <View style={[styles.inputContainer, errors.deposit_date && styles.inputError]}>
            <Calendar size={20} color="#6B7280" style={styles.inputIcon} />
            <TextInput
              style={styles.input}
              placeholder="YYYY-MM-DD"
              value={formData.deposit_date}
              onChangeText={(value) => {
                setFormData(prev => ({ ...prev, deposit_date: value }));
                if (errors.deposit_date) setErrors(prev => ({ ...prev, deposit_date: '' }));
              }}
            />
          </View>
          {errors.deposit_date && <Text style={styles.errorText}>{errors.deposit_date}</Text>}
        </View>

        {/* Description */}
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Keterangan Tambahan</Text>
          <View style={styles.inputContainer}>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Keterangan tambahan (opsional)"
              value={formData.description}
              onChangeText={(value) => setFormData(prev => ({ ...prev, description: value }))}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity 
          style={styles.cancelButton} 
          onPress={onCancel}
          disabled={loading}
        >
          <Text style={styles.cancelButtonText}>Batal</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.submitButton, loading && styles.submitButtonDisabled]} 
          onPress={handleSubmit}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator size="small" color="#FFFFFF" />
          ) : (
            <Text style={styles.submitButtonText}>Simpan Setoran</Text>
          )}
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8FAFC',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
  userInfoCard: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  userInfoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  userInfoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0A2463',
    marginLeft: 8,
  },
  userInfoContent: {
    gap: 8,
  },
  userInfoLabel: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '500',
  },
  userInfoValue: {
    fontSize: 14,
    color: '#1F2937',
    fontWeight: '500',
    marginBottom: 8,
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    marginTop: 0,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  required: {
    color: '#EF4444',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 12,
    minHeight: 48,
  },
  inputError: {
    borderColor: '#EF4444',
  },
  inputIcon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
    paddingVertical: 12,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  currencyPrefix: {
    fontSize: 16,
    color: '#6B7280',
    marginRight: 4,
  },
  amountInput: {
    textAlign: 'right',
  },
  pickerContainer: {
    flex: 1,
    gap: 8,
  },
  categoryOption: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  categoryOptionSelected: {
    borderColor: '#0A2463',
    backgroundColor: '#EEF2FF',
  },
  categoryOptionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  categoryOptionTextSelected: {
    color: '#0A2463',
  },
  categoryDescription: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    padding: 16,
    paddingBottom: 32,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
  },
  submitButton: {
    flex: 2,
    paddingVertical: 14,
    borderRadius: 8,
    backgroundColor: '#0A2463',
    alignItems: 'center',
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
