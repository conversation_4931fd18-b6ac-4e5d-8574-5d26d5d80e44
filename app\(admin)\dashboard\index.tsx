import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { Users, FileText, TrendingUp, Settings, Plus, Eye } from 'lucide-react-native';

import StatusBar from '@/components/StatusBar';
import { useAuth } from '@/contexts/AuthContext';

export default function AdminDashboardScreen() {
  const { profile } = useAuth();

  // Mock admin stats
  const adminStats = {
    totalUsers: 25,
    totalDeposits: 150,
    pendingApprovals: 8,
    monthlyRevenue: 15750000,
  };

  const quickActions = [
    {
      id: 1,
      title: 'Tambah User Baru',
      description: 'Daftarkan user baru ke sistem',
      icon: Plus,
      color: '#10B981',
      action: () => router.push('/(admin)/users/add'),
    },
    {
      id: 2,
      title: 'Kelola User',
      description: 'Lihat dan kelola semua user',
      icon: Users,
      color: '#3B82F6',
      action: () => router.push('/(admin)/users'),
    },
    {
      id: 3,
      title: 'Review Setoran',
      description: 'Approve/reject setoran pending',
      icon: Eye,
      color: '#F59E0B',
      action: () => router.push('/(admin)/reports'),
    },
    {
      id: 4,
      title: 'Pengaturan Sistem',
      description: 'Konfigurasi retribusi dan sistem',
      icon: Settings,
      color: '#8B5CF6',
      action: () => router.push('/(admin)/settings'),
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Admin Panel</Text>
            <Text style={styles.userName}>{profile?.full_name}</Text>
          </View>
        </View>

        {/* Stats Cards */}
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            <View style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: '#EBF5FF' }]}>
                <Users size={24} color="#3B82F6" />
              </View>
              <Text style={styles.statValue}>{adminStats.totalUsers}</Text>
              <Text style={styles.statLabel}>Total User</Text>
            </View>

            <View style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: '#F0FDF4' }]}>
                <FileText size={24} color="#10B981" />
              </View>
              <Text style={styles.statValue}>{adminStats.totalDeposits}</Text>
              <Text style={styles.statLabel}>Total Setoran</Text>
            </View>
          </View>

          <View style={styles.statsRow}>
            <View style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: '#FEF3C7' }]}>
                <TrendingUp size={24} color="#F59E0B" />
              </View>
              <Text style={styles.statValue}>{adminStats.pendingApprovals}</Text>
              <Text style={styles.statLabel}>Pending Approval</Text>
            </View>

            <View style={styles.statCard}>
              <View style={[styles.statIcon, { backgroundColor: '#F0F9FF' }]}>
                <TrendingUp size={24} color="#0EA5E9" />
              </View>
              <Text style={styles.statValue}>Rp {adminStats.monthlyRevenue.toLocaleString()}</Text>
              <Text style={styles.statLabel}>Revenue Bulan Ini</Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <Text style={styles.sectionTitle}>Aksi Cepat</Text>
          
          <View style={styles.actionsGrid}>
            {quickActions.map((action) => (
              <TouchableOpacity
                key={action.id}
                style={styles.actionCard}
                onPress={action.action}
              >
                <View style={[styles.actionIcon, { backgroundColor: `${action.color}15` }]}>
                  <action.icon size={24} color={action.color} />
                </View>
                <Text style={styles.actionTitle}>{action.title}</Text>
                <Text style={styles.actionDescription}>{action.description}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recent Activity */}
        <View style={styles.activityContainer}>
          <Text style={styles.sectionTitle}>Aktivitas Terbaru</Text>
          
          <View style={styles.activityList}>
            <View style={styles.activityItem}>
              <View style={styles.activityDot} />
              <View style={styles.activityContent}>
                <Text style={styles.activityText}>User baru "Ahmad Fauzi" didaftarkan</Text>
                <Text style={styles.activityTime}>2 jam yang lalu</Text>
              </View>
            </View>

            <View style={styles.activityItem}>
              <View style={styles.activityDot} />
              <View style={styles.activityContent}>
                <Text style={styles.activityText}>Setoran Rp 50.000 diapprove</Text>
                <Text style={styles.activityTime}>4 jam yang lalu</Text>
              </View>
            </View>

            <View style={styles.activityItem}>
              <View style={styles.activityDot} />
              <View style={styles.activityContent}>
                <Text style={styles.activityText}>Kategori "Perizinan" ditambahkan</Text>
                <Text style={styles.activityTime}>1 hari yang lalu</Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  greeting: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    marginTop: 4,
  },
  statsContainer: {
    padding: 16,
    gap: 12,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statValue: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'center',
  },
  actionsContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  actionsGrid: {
    gap: 12,
  },
  actionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    flex: 1,
  },
  actionDescription: {
    fontSize: 12,
    color: '#6B7280',
    flex: 1,
    marginTop: 2,
  },
  activityContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  activityList: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  activityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#3B82F6',
    marginTop: 6,
    marginRight: 12,
  },
  activityContent: {
    flex: 1,
  },
  activityText: {
    fontSize: 14,
    color: '#1F2937',
    fontWeight: '500',
  },
  activityTime: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
});
