import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Plus, Search, Edit, Trash2, FileText, ToggleLeft, ToggleRight, Hash } from 'lucide-react-native';

import { jenisRetribusiService, type JenisRetribusiWithStats } from '../../../services/jenisRetribusiService';
import { useAuth } from '../../../contexts/AuthContext';

export default function JenisRetribusiManagementScreen() {
  const { user } = useAuth();
  const [jenisRetribusi, setJenisRetribusi] = useState<JenisRetribusiWithStats[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredJenis, setFilteredJenis] = useState<JenisRetribusiWithStats[]>([]);

  // Check if user is admin
  const isAdmin = user?.role === 'ADMIN';

  useEffect(() => {
    if (!isAdmin) {
      Alert.alert('Access Denied', 'Anda tidak memiliki akses ke halaman ini.');
      router.back();
      return;
    }
    loadJenisRetribusi();
  }, [isAdmin]);

  useEffect(() => {
    // Filter jenis retribusi based on search query
    if (searchQuery.trim() === '') {
      setFilteredJenis(jenisRetribusi);
    } else {
      const filtered = jenisRetribusi.filter(jenis =>
        jenis.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        jenis.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        jenis.base_account_number.includes(searchQuery) ||
        jenis.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredJenis(filtered);
    }
  }, [searchQuery, jenisRetribusi]);

  const loadJenisRetribusi = async () => {
    try {
      setLoading(true);
      const data = await jenisRetribusiService.getAllJenisRetribusi(true);
      setJenisRetribusi(data);
    } catch (error) {
      console.error('Error loading jenis retribusi:', error);
      Alert.alert('Error', 'Gagal memuat data jenis retribusi');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadJenisRetribusi();
    setRefreshing(false);
  };

  const handleToggleStatus = async (jenis: JenisRetribusiWithStats) => {
    try {
      const action = jenis.is_active ? 'nonaktifkan' : 'aktifkan';
      Alert.alert(
        'Konfirmasi',
        `Apakah Anda yakin ingin ${action} jenis retribusi "${jenis.name}"?`,
        [
          { text: 'Batal', style: 'cancel' },
          {
            text: 'Ya',
            onPress: async () => {
              await jenisRetribusiService.toggleJenisRetribusiStatus(jenis.id);
              await loadJenisRetribusi();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error toggling jenis retribusi status:', error);
      Alert.alert('Error', 'Gagal mengubah status jenis retribusi');
    }
  };

  const handleDelete = async (jenis: JenisRetribusiWithStats) => {
    try {
      const hasRetribusi = (jenis.retribusi_count || 0) > 0;

      let message = `Apakah Anda yakin ingin menghapus jenis retribusi "${jenis.name}"?`;
      
      if (hasRetribusi) {
        message += '\n\nJenis retribusi ini memiliki kategori retribusi terkait dan akan dinonaktifkan saja.';
      }

      Alert.alert('Konfirmasi Hapus', message, [
        { text: 'Batal', style: 'cancel' },
        {
          text: 'Hapus',
          style: 'destructive',
          onPress: async () => {
            await jenisRetribusiService.deleteJenisRetribusi(jenis.id);
            await loadJenisRetribusi();
          },
        },
      ]);
    } catch (error) {
      console.error('Error deleting jenis retribusi:', error);
      Alert.alert('Error', 'Gagal menghapus jenis retribusi');
    }
  };

  const renderJenisItem = ({ item }: { item: JenisRetribusiWithStats }) => (
    <View style={[styles.jenisCard, !item.is_active && styles.inactiveCard]}>
      <View style={styles.jenisHeader}>
        <View style={styles.jenisInfo}>
          <Text style={styles.jenisName}>{item.name}</Text>
          <View style={styles.codeRow}>
            <Text style={styles.jenisCode}>{item.code}</Text>
            <View style={styles.accountNumber}>
              <Hash size={12} color="#6B7280" />
              <Text style={styles.accountText}>{item.base_account_number}</Text>
            </View>
          </View>
          {item.description && (
            <Text style={styles.jenisDescription}>{item.description}</Text>
          )}
        </View>
        <View style={styles.statusBadge}>
          <Text style={[styles.statusText, item.is_active ? styles.activeText : styles.inactiveText]}>
            {item.is_active ? 'Aktif' : 'Nonaktif'}
          </Text>
        </View>
      </View>

      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <FileText size={16} color="#6B7280" />
          <Text style={styles.statText}>{item.retribusi_count || 0} Kategori Retribusi</Text>
        </View>
      </View>

      <View style={styles.actionRow}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => router.push(`/(admin)/jenis-retribusi/${item.id}`)}
        >
          <Edit size={16} color="#3B82F6" />
          <Text style={styles.actionText}>Edit</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => handleToggleStatus(item)}
        >
          {item.is_active ? (
            <ToggleRight size={16} color="#10B981" />
          ) : (
            <ToggleLeft size={16} color="#6B7280" />
          )}
          <Text style={styles.actionText}>
            {item.is_active ? 'Nonaktifkan' : 'Aktifkan'}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.deleteButton]}
          onPress={() => handleDelete(item)}
        >
          <Trash2 size={16} color="#EF4444" />
          <Text style={[styles.actionText, styles.deleteText]}>Hapus</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (!isAdmin) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Jenis Retribusi</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => router.push('/(admin)/jenis-retribusi/create')}
        >
          <Plus size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <View style={styles.searchContainer}>
        <Search size={20} color="#6B7280" />
        <TextInput
          style={styles.searchInput}
          placeholder="Cari jenis retribusi..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      <FlatList
        data={filteredJenis}
        renderItem={renderJenisItem}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {searchQuery ? 'Tidak ada jenis retribusi yang ditemukan' : 'Belum ada jenis retribusi'}
            </Text>
          </View>
        }
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  addButton: {
    backgroundColor: '#3B82F6',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginVertical: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  searchInput: {
    flex: 1,
    marginLeft: 12,
    fontSize: 16,
    color: '#111827',
  },
  listContainer: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  jenisCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  inactiveCard: {
    opacity: 0.6,
    borderColor: '#D1D5DB',
  },
  jenisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  jenisInfo: {
    flex: 1,
  },
  jenisName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 6,
  },
  codeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  jenisCode: {
    fontSize: 14,
    fontWeight: '500',
    color: '#3B82F6',
    marginRight: 12,
  },
  accountNumber: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3F4F6',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  accountText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#6B7280',
    marginLeft: 4,
  },
  jenisDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
    backgroundColor: '#F3F4F6',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  activeText: {
    color: '#10B981',
  },
  inactiveText: {
    color: '#6B7280',
  },
  statsRow: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 20,
  },
  statText: {
    fontSize: 14,
    color: '#6B7280',
    marginLeft: 6,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  actionText: {
    fontSize: 14,
    color: '#3B82F6',
    marginLeft: 6,
    fontWeight: '500',
  },
  deleteButton: {
    backgroundColor: '#FEF2F2',
  },
  deleteText: {
    color: '#EF4444',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
});
