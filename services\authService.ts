import { supabase } from '../lib/supabase';
import type { User, UserInsert } from '../lib/supabase';

export interface AuthUser {
  id: string;
  email: string;
  full_name: string;
  role: 'ADMIN' | 'USER' | 'SUPERVISOR';
  department: string | null;
  position: string | null;
  phone: string | null;
  is_active: boolean;
}

export interface SignInResponse {
  user: any;
  profile: AuthUser | null;
}

export class AuthService {
  /**
   * Sign in user dengan email dan password
   */
  static async signIn(email: string, password: string): Promise<SignInResponse> {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Get user profile dari database
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('*')
        .eq('id', data.user.id)
        .single();

      if (profileError) {
        console.error('Error fetching profile:', profileError);
        throw new Error('Failed to load user profile');
      }

      return { user: data.user, profile };
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    }
  }

  /**
   * Register user baru (hanya bisa dilakukan oleh admin)
   */
  static async signUp(userData: {
    email: string;
    password: string;
    full_name: string;
    role?: 'ADMIN' | 'USER' | 'SUPERVISOR';
    department?: string;
    position?: string;
    phone?: string;
  }) {
    try {
      // Cek apakah user yang sedang login adalah admin
      const { data: currentUser } = await supabase.auth.getUser();
      if (!currentUser.user) {
        throw new Error('Unauthorized: No user logged in');
      }

      const { data: currentProfile } = await supabase
        .from('users')
        .select('role')
        .eq('id', currentUser.user.id)
        .single();

      if (currentProfile?.role !== 'ADMIN') {
        throw new Error('Unauthorized: Only admins can create users');
      }

      // Create auth user menggunakan service role
      const { data, error } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true,
      });

      if (error) throw error;

      // Create user profile
      const profileData: UserInsert = {
        id: data.user.id,
        email: userData.email,
        full_name: userData.full_name,
        role: userData.role || 'USER',
        department: userData.department || null,
        position: userData.position || null,
        phone: userData.phone || null,
        created_by: currentUser.user.id,
      };

      const { error: profileError } = await supabase
        .from('users')
        .insert(profileData);

      if (profileError) {
        // Rollback: delete auth user jika profile creation gagal
        await supabase.auth.admin.deleteUser(data.user.id);
        throw profileError;
      }

      return data;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }

  /**
   * Sign out user
   */
  static async signOut(): Promise<void> {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  }

  /**
   * Get current user dengan profile
   */
  static async getCurrentUser(): Promise<{ user: any; profile: AuthUser | null } | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user) return null;

      const { data: profile, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching profile:', error);
        return { user, profile: null };
      }

      return { user, profile };
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(userId: string, updates: {
    full_name?: string;
    department?: string;
    position?: string;
    phone?: string;
    address?: string;
  }): Promise<AuthUser> {
    try {
      const { data, error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  /**
   * Change password
   */
  static async changePassword(newPassword: string): Promise<void> {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  /**
   * Reset password (send email)
   */
  static async resetPassword(email: string): Promise<void> {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      if (error) throw error;
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    }
  }

  /**
   * Check if user has specific role
   */
  static async hasRole(userId: string, role: 'ADMIN' | 'USER' | 'SUPERVISOR'): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('role')
        .eq('id', userId)
        .single();

      if (error) return false;
      return data.role === role;
    } catch (error) {
      console.error('Check role error:', error);
      return false;
    }
  }

  /**
   * Get all users (admin only)
   */
  static async getAllUsers(): Promise<AuthUser[]> {
    try {
      // Verify admin access
      const { data: currentUser } = await supabase.auth.getUser();
      if (!currentUser.user) throw new Error('Unauthorized');

      const isAdmin = await this.hasRole(currentUser.user.id, 'ADMIN');
      if (!isAdmin) throw new Error('Admin access required');

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Get all users error:', error);
      throw error;
    }
  }

  /**
   * Deactivate user (admin only)
   */
  static async deactivateUser(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: false })
        .eq('id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Deactivate user error:', error);
      throw error;
    }
  }

  /**
   * Activate user (admin only)
   */
  static async activateUser(userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('users')
        .update({ is_active: true })
        .eq('id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Activate user error:', error);
      throw error;
    }
  }
}
