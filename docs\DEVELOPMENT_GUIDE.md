# Development Guide - Report Retribusi

Panduan lengkap untuk pengembangan aplikasi Report Retribusi.

## 🏗️ Setup Development Environment

### 1. Prerequisites

```bash
# Install Node.js (versi 18+)
# Download dari https://nodejs.org/

# Install Bun (package manager)
curl -fsSL https://bun.sh/install | bash

# Install Expo CLI global
bun add -g @expo/cli

# Install Git
# Download dari https://git-scm.com/
```

### 2. Clone dan Setup Project

```bash
# Clone repository
git clone <repository-url>
cd report-retribusi

# Install dependencies
bun install

# Verifikasi instalasi
bun run dev
```

## 📱 Development Commands

### Basic Commands

```bash
# Jalankan development server
bun run dev

# Build untuk web
bun run build:web

# Linting code
bun run lint

# Update dependencies
bun update
```

### Expo Specific Commands

```bash
# Start dengan clear cache
expo start --clear

# Start dengan tunnel (untuk testing di device fisik)
expo start --tunnel

# Build preview
expo build:web

# Publish ke Expo
expo publish
```

## 🏗️ Arsitektur Aplikasi

### File-based Routing (Expo Router)

```
app/
├── _layout.tsx           # Root layout dengan providers
├── index.tsx            # Home screen (/)
├── +not-found.tsx       # 404 page
├── (tabs)/              # Tab group
│   ├── _layout.tsx      # Tab layout
│   ├── index.tsx        # Tab home (/tabs)
│   ├── profile.tsx      # Profile tab (/tabs/profile)
│   └── settings.tsx     # Settings tab (/tabs/settings)
└── auth/                # Auth group
    ├── login.tsx        # Login screen (/auth/login)
    └── register.tsx     # Register screen (/auth/register)
```

### Component Structure

```
components/
├── ui/                  # Basic UI components
│   ├── Button.tsx
│   ├── Input.tsx
│   └── Card.tsx
├── forms/               # Form components
│   ├── LoginForm.tsx
│   └── ReportForm.tsx
├── layout/              # Layout components
│   ├── Header.tsx
│   └── TabBar.tsx
└── StatusBar.tsx        # Status bar component
```

## 🎨 Styling Guidelines

### 1. StyleSheet Pattern

```typescript
import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
});
```

### 2. Color Palette

```typescript
// colors.ts
export const colors = {
  primary: '#007AFF',
  secondary: '#5856D6',
  success: '#34C759',
  warning: '#FF9500',
  error: '#FF3B30',
  background: '#F2F2F7',
  surface: '#FFFFFF',
  text: '#000000',
  textSecondary: '#8E8E93',
};
```

### 3. Typography

```typescript
// typography.ts
export const typography = {
  h1: { fontSize: 32, fontWeight: 'bold' },
  h2: { fontSize: 24, fontWeight: 'bold' },
  h3: { fontSize: 20, fontWeight: '600' },
  body: { fontSize: 16, fontWeight: 'normal' },
  caption: { fontSize: 12, fontWeight: 'normal' },
};
```

## 🔧 State Management

### 1. Local State (useState)

```typescript
import { useState } from 'react';

export default function MyComponent() {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState(null);
  
  // Component logic
}
```

### 2. Custom Hooks

```typescript
// hooks/useApi.ts
import { useState, useEffect } from 'react';

export function useApi<T>(url: string) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch logic
  }, [url]);

  return { data, loading, error };
}
```

## 📡 API Integration

### 1. API Service

```typescript
// services/api.ts
const API_BASE_URL = 'https://api.example.com';

export class ApiService {
  static async get(endpoint: string) {
    const response = await fetch(`${API_BASE_URL}${endpoint}`);
    return response.json();
  }

  static async post(endpoint: string, data: any) {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    return response.json();
  }
}
```

### 2. Data Fetching Pattern

```typescript
// hooks/useRetribusi.ts
import { useEffect, useState } from 'react';
import { ApiService } from '../services/api';

export function useRetribusi() {
  const [retribusi, setRetribusi] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await ApiService.get('/retribusi');
        setRetribusi(data);
      } catch (error) {
        console.error('Error fetching retribusi:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  return { retribusi, loading };
}
```

## 📸 Camera Integration

### Setup Camera

```typescript
import { Camera, CameraType } from 'expo-camera';
import { useState } from 'react';

export function CameraScreen() {
  const [type, setType] = useState(CameraType.back);
  const [permission, requestPermission] = Camera.useCameraPermissions();

  if (!permission) {
    return <View />;
  }

  if (!permission.granted) {
    return (
      <View>
        <Button onPress={requestPermission} title="Grant Camera Permission" />
      </View>
    );
  }

  return (
    <Camera style={{ flex: 1 }} type={type}>
      {/* Camera UI */}
    </Camera>
  );
}
```

## 🧪 Testing

### 1. Component Testing

```typescript
// __tests__/Button.test.tsx
import { render, fireEvent } from '@testing-library/react-native';
import Button from '../components/Button';

describe('Button Component', () => {
  it('renders correctly', () => {
    const { getByText } = render(<Button title="Test" />);
    expect(getByText('Test')).toBeTruthy();
  });

  it('handles press events', () => {
    const onPress = jest.fn();
    const { getByText } = render(<Button title="Test" onPress={onPress} />);
    
    fireEvent.press(getByText('Test'));
    expect(onPress).toHaveBeenCalled();
  });
});
```

### 2. Running Tests

```bash
# Install testing dependencies
bun add -d @testing-library/react-native jest

# Run tests
bun test

# Run tests with coverage
bun test --coverage
```

## 🚀 Deployment

### 1. Web Deployment

```bash
# Build untuk web
bun run build:web

# Deploy ke Netlify/Vercel
# Upload folder dist/ ke hosting provider
```

### 2. Mobile App Store

```bash
# Build untuk production
expo build:android
expo build:ios

# Submit ke store
expo submit:android
expo submit:ios
```

## 🔍 Debugging

### 1. React Native Debugger

```bash
# Install React Native Debugger
# Download dari https://github.com/jhen0409/react-native-debugger

# Enable debugging
# Shake device atau Cmd+D (iOS) / Cmd+M (Android)
# Select "Debug with Chrome"
```

### 2. Flipper Integration

```bash
# Install Flipper
# Download dari https://fbflipper.com/

# Add Flipper plugins untuk network, layout, dll
```

## 📋 Code Quality

### 1. ESLint Configuration

```json
// .eslintrc.js
module.exports = {
  extends: ['expo', '@react-native-community'],
  rules: {
    'react-native/no-unused-styles': 'error',
    'react-native/split-platform-components': 'error',
  },
};
```

### 2. Prettier Configuration

```json
// .prettierrc
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

## 🔄 Git Workflow

### Branch Strategy

```bash
# Main branches
main          # Production ready code
develop       # Development branch

# Feature branches
feature/auth-system
feature/camera-integration
feature/report-form

# Hotfix branches
hotfix/critical-bug-fix
```

### Commit Convention

```bash
# Format: type(scope): description
feat(auth): add login functionality
fix(camera): resolve permission issue
docs(readme): update installation guide
style(ui): improve button styling
refactor(api): optimize data fetching
test(auth): add login form tests
```

---

**Happy Coding! 🚀**
