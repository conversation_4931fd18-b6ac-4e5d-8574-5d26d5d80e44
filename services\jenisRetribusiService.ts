import { supabase } from '../lib/supabase';
import type { JenisRetribusi, JenisRetribusiInsert, JenisRetribusiUpdate } from '../lib/supabase';

export interface JenisRetribusiWithStats extends JenisRetribusi {
  retribusi_count?: number;
}

class JenisRetribusiService {
  // Get all jenis retribusi with optional stats
  async getAllJenisRetribusi(includeStats = false): Promise<JenisRetribusiWithStats[]> {
    try {
      let query = supabase
        .from('jenis_retribusi')
        .select('*')
        .order('name');

      const { data, error } = await query;

      if (error) throw error;

      if (!includeStats) {
        return data || [];
      }

      // Get stats for each jenis retribusi
      const jenisWithStats = await Promise.all(
        (data || []).map(async (jenis) => {
          // Get retribusi count
          const { count: retribusiCount } = await supabase
            .from('retribusi_categories')
            .select('*', { count: 'exact', head: true })
            .eq('jenis_retribusi_id', jenis.id);

          return {
            ...jenis,
            retribusi_count: retribusiCount || 0,
          };
        })
      );

      return jenisWithStats;
    } catch (error) {
      console.error('Error fetching jenis retribusi:', error);
      throw error;
    }
  }

  // Get active jenis retribusi only
  async getActiveJenisRetribusi(): Promise<JenisRetribusi[]> {
    try {
      const { data, error } = await supabase
        .from('jenis_retribusi')
        .select('*')
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching active jenis retribusi:', error);
      throw error;
    }
  }

  // Get jenis retribusi by ID
  async getJenisRetribusiById(id: string): Promise<JenisRetribusi | null> {
    try {
      const { data, error } = await supabase
        .from('jenis_retribusi')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching jenis retribusi:', error);
      throw error;
    }
  }

  // Create new jenis retribusi
  async createJenisRetribusi(jenisData: JenisRetribusiInsert): Promise<JenisRetribusi> {
    try {
      const { data, error } = await supabase
        .from('jenis_retribusi')
        .insert(jenisData)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating jenis retribusi:', error);
      throw error;
    }
  }

  // Update jenis retribusi
  async updateJenisRetribusi(id: string, jenisData: JenisRetribusiUpdate): Promise<JenisRetribusi> {
    try {
      const { data, error } = await supabase
        .from('jenis_retribusi')
        .update(jenisData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating jenis retribusi:', error);
      throw error;
    }
  }

  // Delete jenis retribusi (soft delete by setting is_active = false)
  async deleteJenisRetribusi(id: string): Promise<void> {
    try {
      // Check if jenis retribusi has retribusi categories
      const { count: retribusiCount } = await supabase
        .from('retribusi_categories')
        .select('*', { count: 'exact', head: true })
        .eq('jenis_retribusi_id', id);

      if ((retribusiCount || 0) > 0) {
        // Soft delete if has dependencies
        const { error } = await supabase
          .from('jenis_retribusi')
          .update({ is_active: false })
          .eq('id', id);

        if (error) throw error;
      } else {
        // Hard delete if no dependencies
        const { error } = await supabase
          .from('jenis_retribusi')
          .delete()
          .eq('id', id);

        if (error) throw error;
      }
    } catch (error) {
      console.error('Error deleting jenis retribusi:', error);
      throw error;
    }
  }

  // Toggle jenis retribusi active status
  async toggleJenisRetribusiStatus(id: string): Promise<JenisRetribusi> {
    try {
      // Get current status
      const jenis = await this.getJenisRetribusiById(id);
      if (!jenis) throw new Error('Jenis retribusi not found');

      // Toggle status
      const { data, error } = await supabase
        .from('jenis_retribusi')
        .update({ is_active: !jenis.is_active })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error toggling jenis retribusi status:', error);
      throw error;
    }
  }

  // Search jenis retribusi
  async searchJenisRetribusi(query: string): Promise<JenisRetribusi[]> {
    try {
      const { data, error } = await supabase
        .from('jenis_retribusi')
        .select('*')
        .or(`name.ilike.%${query}%,code.ilike.%${query}%,description.ilike.%${query}%`)
        .eq('is_active', true)
        .order('name');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error searching jenis retribusi:', error);
      throw error;
    }
  }

  // Validate base account number format
  validateBaseAccountNumber(baseAccountNumber: string): boolean {
    // Format should be like: *********, *********, etc.
    const pattern = /^4\.1\.01\.\d{2}$/;
    return pattern.test(baseAccountNumber);
  }

  // Generate next base account number
  async generateNextBaseAccountNumber(): Promise<string> {
    try {
      const { data, error } = await supabase
        .from('jenis_retribusi')
        .select('base_account_number')
        .like('base_account_number', '4.1.01.%')
        .order('base_account_number', { ascending: false })
        .limit(1);

      if (error) throw error;

      if (!data || data.length === 0) {
        // Start with ********* if no existing data
        return '*********';
      }

      // Extract the last two digits and increment
      const lastNumber = data[0].base_account_number;
      const lastDigits = parseInt(lastNumber.split('.').pop() || '00');
      const nextDigits = (lastDigits + 1).toString().padStart(2, '0');
      
      return `4.1.01.${nextDigits}`;
    } catch (error) {
      console.error('Error generating next base account number:', error);
      throw error;
    }
  }

  // Get retribusi categories by jenis
  async getRetribusiCategoriesByJenis(jenisId: string): Promise<any[]> {
    try {
      const { data, error } = await supabase
        .from('retribusi_categories')
        .select(`
          id,
          nomor_rekening,
          nama_retribusi,
          description,
          base_tariff,
          is_active,
          opd:opd_id (
            name,
            code
          )
        `)
        .eq('jenis_retribusi_id', jenisId)
        .order('nomor_rekening');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching retribusi categories by jenis:', error);
      throw error;
    }
  }
}

export const jenisRetribusiService = new JenisRetribusiService();
