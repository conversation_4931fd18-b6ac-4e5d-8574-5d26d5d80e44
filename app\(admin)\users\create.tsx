import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  Switch,
  FlatList,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Save, X, Check } from 'lucide-react-native';

import { userService, type CreateUserData } from '../../../services/userService';
import { opdService, type OPD } from '../../../services/opdService';
import { useAuth } from '../../../contexts/AuthContext';

interface UserFormData {
  email: string;
  password: string;
  confirmPassword: string;
  full_name: string;
  nip: string;
  role: 'USER' | 'SUPERVISOR' | 'ADMIN';
  position: string;
  phone: string;
  address: string;
  opdIds: string[];
}

export default function CreateUserScreen() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [opds, setOPDs] = useState<OPD[]>([]);
  const [formData, setFormData] = useState<UserFormData>({
    email: '',
    password: '',
    confirmPassword: '',
    full_name: '',
    nip: '',
    role: 'USER',
    position: '',
    phone: '',
    address: '',
    opdIds: [],
  });

  // Check if user is admin
  const isAdmin = user?.role === 'ADMIN';

  useEffect(() => {
    if (!isAdmin) {
      Alert.alert('Access Denied', 'Anda tidak memiliki akses ke halaman ini.');
      router.back();
      return;
    }
    loadOPDs();
  }, [isAdmin]);

  const loadOPDs = async () => {
    try {
      const data = await opdService.getActiveOPDs();
      setOPDs(data);
    } catch (error) {
      console.error('Error loading OPDs:', error);
      Alert.alert('Error', 'Gagal memuat data OPD');
    }
  };

  const handleInputChange = (field: keyof UserFormData, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleOPDToggle = (opdId: string) => {
    setFormData(prev => ({
      ...prev,
      opdIds: prev.opdIds.includes(opdId)
        ? prev.opdIds.filter(id => id !== opdId)
        : [...prev.opdIds, opdId],
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.email.trim()) {
      Alert.alert('Error', 'Email harus diisi');
      return false;
    }

    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      Alert.alert('Error', 'Format email tidak valid');
      return false;
    }

    if (!formData.password) {
      Alert.alert('Error', 'Password harus diisi');
      return false;
    }

    if (formData.password.length < 6) {
      Alert.alert('Error', 'Password minimal 6 karakter');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('Error', 'Konfirmasi password tidak cocok');
      return false;
    }

    if (!formData.full_name.trim()) {
      Alert.alert('Error', 'Nama lengkap harus diisi');
      return false;
    }

    if (formData.opdIds.length === 0) {
      Alert.alert('Error', 'User harus di-assign ke minimal 1 OPD');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);

      const userData: CreateUserData = {
        email: formData.email.trim(),
        password: formData.password,
        full_name: formData.full_name.trim(),
        nip: formData.nip.trim() || undefined,
        role: formData.role,
        position: formData.position.trim() || undefined,
        phone: formData.phone.trim() || undefined,
        address: formData.address.trim() || undefined,
        opdIds: formData.opdIds,
      };

      await userService.createUser(userData);

      Alert.alert(
        'Berhasil',
        'User berhasil dibuat',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Error creating user:', error);
      
      let errorMessage = 'Gagal membuat user';
      if (error.message?.includes('duplicate key')) {
        if (error.message.includes('email')) {
          errorMessage = 'Email sudah digunakan';
        } else if (error.message.includes('nip')) {
          errorMessage = 'NIP sudah digunakan';
        }
      } else if (error.message?.includes('User already registered')) {
        errorMessage = 'Email sudah terdaftar';
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const renderOPDItem = ({ item }: { item: OPD }) => {
    const isSelected = formData.opdIds.includes(item.id);
    
    return (
      <TouchableOpacity
        style={[styles.opdItem, isSelected && styles.opdItemSelected]}
        onPress={() => handleOPDToggle(item.id)}
      >
        <View style={styles.opdInfo}>
          <Text style={[styles.opdName, isSelected && styles.opdNameSelected]}>
            {item.name}
          </Text>
          <Text style={[styles.opdCode, isSelected && styles.opdCodeSelected]}>
            {item.code}
          </Text>
        </View>
        {isSelected && (
          <Check size={20} color="#3B82F6" />
        )}
      </TouchableOpacity>
    );
  };

  if (!isAdmin) {
    return null;
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => router.back()}
        >
          <X size={24} color="#6B7280" />
        </TouchableOpacity>
        <Text style={styles.title}>Tambah User</Text>
        <TouchableOpacity
          style={[styles.saveButton, loading && styles.disabledButton]}
          onPress={handleSave}
          disabled={loading}
        >
          <Save size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Akun</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email *</Text>
            <TextInput
              style={styles.input}
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
              placeholder="<EMAIL>"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Password *</Text>
            <TextInput
              style={styles.input}
              value={formData.password}
              onChangeText={(value) => handleInputChange('password', value)}
              placeholder="Minimal 6 karakter"
              secureTextEntry
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Konfirmasi Password *</Text>
            <TextInput
              style={styles.input}
              value={formData.confirmPassword}
              onChangeText={(value) => handleInputChange('confirmPassword', value)}
              placeholder="Ulangi password"
              secureTextEntry
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Personal</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nama Lengkap *</Text>
            <TextInput
              style={styles.input}
              value={formData.full_name}
              onChangeText={(value) => handleInputChange('full_name', value)}
              placeholder="Nama lengkap"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>NIP</Text>
            <TextInput
              style={styles.input}
              value={formData.nip}
              onChangeText={(value) => handleInputChange('nip', value)}
              placeholder="Nomor Induk Pegawai"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Jabatan</Text>
            <TextInput
              style={styles.input}
              value={formData.position}
              onChangeText={(value) => handleInputChange('position', value)}
              placeholder="Jabatan"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nomor Telepon</Text>
            <TextInput
              style={styles.input}
              value={formData.phone}
              onChangeText={(value) => handleInputChange('phone', value)}
              placeholder="Nomor telepon"
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Alamat</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.address}
              onChangeText={(value) => handleInputChange('address', value)}
              placeholder="Alamat lengkap"
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Role & Akses</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Role</Text>
            <View style={styles.roleContainer}>
              {(['USER', 'SUPERVISOR', 'ADMIN'] as const).map((role) => (
                <TouchableOpacity
                  key={role}
                  style={[
                    styles.roleButton,
                    formData.role === role && styles.roleButtonSelected,
                  ]}
                  onPress={() => handleInputChange('role', role)}
                >
                  <Text
                    style={[
                      styles.roleText,
                      formData.role === role && styles.roleTextSelected,
                    ]}
                  >
                    {role}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Pilih OPD * ({formData.opdIds.length} dipilih)</Text>
          <Text style={styles.sectionSubtitle}>
            User harus di-assign ke minimal 1 OPD
          </Text>
          
          <FlatList
            data={opds}
            renderItem={renderOPDItem}
            keyExtractor={(item) => item.id}
            scrollEnabled={false}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
          />
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  cancelButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  saveButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  roleContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  roleButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  roleButtonSelected: {
    borderColor: '#3B82F6',
    backgroundColor: '#EFF6FF',
  },
  roleText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  roleTextSelected: {
    color: '#3B82F6',
  },
  opdItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#FFFFFF',
  },
  opdItemSelected: {
    borderColor: '#3B82F6',
    backgroundColor: '#EFF6FF',
  },
  opdInfo: {
    flex: 1,
  },
  opdName: {
    fontSize: 14,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  opdNameSelected: {
    color: '#1D4ED8',
  },
  opdCode: {
    fontSize: 12,
    color: '#6B7280',
  },
  opdCodeSelected: {
    color: '#3B82F6',
  },
  separator: {
    height: 8,
  },
  bottomPadding: {
    height: 40,
  },
});
