import { supabase } from '../lib/supabase';
import type { User, UserInsert, UserUpdate, OPD } from '../lib/supabase';
import { opdService } from './opdService';

export interface UserWithOPDs extends User {
  opds?: OPD[];
  opd_count?: number;
}

export interface CreateUserData {
  email: string;
  password: string;
  full_name: string;
  nip?: string;
  role?: 'USER' | 'SUPERVISOR' | 'ADMIN';
  position?: string;
  phone?: string;
  address?: string;
  opdIds: string[];
}

export interface UpdateUserData {
  full_name?: string;
  nip?: string;
  role?: 'USER' | 'SUPERVISOR' | 'ADMIN';
  position?: string;
  phone?: string;
  address?: string;
  is_active?: boolean;
  opdIds?: string[];
}

class UserService {
  // Get all users with OPD information
  async getAllUsers(): Promise<UserWithOPDs[]> {
    try {
      // Verify admin access
      const { data: currentUser } = await supabase.auth.getUser();
      if (!currentUser.user) throw new Error('Unauthorized');

      const { data: currentProfile } = await supabase
        .from('users')
        .select('role')
        .eq('id', currentUser.user.id)
        .single();

      if (currentProfile?.role !== 'ADMIN') {
        throw new Error('Admin access required');
      }

      // Get users with OPD count
      const { data: users, error } = await supabase
        .from('users')
        .select(`
          *,
          user_opd!inner(
            opd:opd_id(*)
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Process users to include OPD information
      const usersWithOPDs = await Promise.all(
        (users || []).map(async (user) => {
          // Get user's OPDs
          const userOPDs = await opdService.getUserOPDs(user.id);
          
          return {
            ...user,
            opds: userOPDs,
            opd_count: userOPDs.length,
          };
        })
      );

      return usersWithOPDs;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  }

  // Get user by ID with OPDs
  async getUserById(id: string): Promise<UserWithOPDs | null> {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      if (!user) return null;

      // Get user's OPDs
      const userOPDs = await opdService.getUserOPDs(user.id);

      return {
        ...user,
        opds: userOPDs,
        opd_count: userOPDs.length,
      };
    } catch (error) {
      console.error('Error fetching user:', error);
      throw error;
    }
  }

  // Create new user (admin only)
  async createUser(userData: CreateUserData): Promise<User> {
    try {
      // Verify admin access
      const { data: currentUser } = await supabase.auth.getUser();
      if (!currentUser.user) throw new Error('Unauthorized');

      const { data: currentProfile } = await supabase
        .from('users')
        .select('role')
        .eq('id', currentUser.user.id)
        .single();

      if (currentProfile?.role !== 'ADMIN') {
        throw new Error('Admin access required');
      }

      // Validate OPDs exist
      if (userData.opdIds.length === 0) {
        throw new Error('User must be assigned to at least one OPD');
      }

      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true,
      });

      if (authError) throw authError;

      try {
        // Create user profile
        const profileData: UserInsert = {
          id: authData.user.id,
          email: userData.email,
          full_name: userData.full_name,
          nip: userData.nip || null,
          role: userData.role || 'USER',
          position: userData.position || null,
          phone: userData.phone || null,
          address: userData.address || null,
          created_by: currentUser.user.id,
        };

        const { data: user, error: profileError } = await supabase
          .from('users')
          .insert(profileData)
          .select()
          .single();

        if (profileError) throw profileError;

        // Assign user to OPDs
        await opdService.assignUserToOPDs(authData.user.id, userData.opdIds);

        return user;
      } catch (error) {
        // Rollback: delete auth user if profile creation fails
        await supabase.auth.admin.deleteUser(authData.user.id);
        throw error;
      }
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // Update user (admin only)
  async updateUser(id: string, userData: UpdateUserData): Promise<UserWithOPDs> {
    try {
      // Verify admin access
      const { data: currentUser } = await supabase.auth.getUser();
      if (!currentUser.user) throw new Error('Unauthorized');

      const { data: currentProfile } = await supabase
        .from('users')
        .select('role')
        .eq('id', currentUser.user.id)
        .single();

      if (currentProfile?.role !== 'ADMIN') {
        throw new Error('Admin access required');
      }

      // Update user profile
      const updateData: UserUpdate = {
        full_name: userData.full_name,
        nip: userData.nip,
        role: userData.role,
        position: userData.position,
        phone: userData.phone,
        address: userData.address,
        is_active: userData.is_active,
      };

      const { data: user, error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Update OPD assignments if provided
      if (userData.opdIds) {
        if (userData.opdIds.length === 0) {
          throw new Error('User must be assigned to at least one OPD');
        }
        await opdService.assignUserToOPDs(id, userData.opdIds);
      }

      // Get updated user with OPDs
      const updatedUser = await this.getUserById(id);
      if (!updatedUser) throw new Error('Failed to fetch updated user');

      return updatedUser;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  // Delete user (admin only)
  async deleteUser(id: string): Promise<void> {
    try {
      // Verify admin access
      const { data: currentUser } = await supabase.auth.getUser();
      if (!currentUser.user) throw new Error('Unauthorized');

      const { data: currentProfile } = await supabase
        .from('users')
        .select('role')
        .eq('id', currentUser.user.id)
        .single();

      if (currentProfile?.role !== 'ADMIN') {
        throw new Error('Admin access required');
      }

      // Check if user has deposits
      const { count: depositCount } = await supabase
        .from('deposits')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', id);

      if ((depositCount || 0) > 0) {
        // Soft delete if has deposits
        await supabase
          .from('users')
          .update({ is_active: false })
          .eq('id', id);
      } else {
        // Hard delete if no deposits
        // First remove OPD assignments
        await supabase
          .from('user_opd')
          .delete()
          .eq('user_id', id);

        // Delete user profile
        await supabase
          .from('users')
          .delete()
          .eq('id', id);

        // Delete auth user
        await supabase.auth.admin.deleteUser(id);
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // Toggle user active status
  async toggleUserStatus(id: string): Promise<User> {
    try {
      // Get current user
      const user = await this.getUserById(id);
      if (!user) throw new Error('User not found');

      // Toggle status
      const { data, error } = await supabase
        .from('users')
        .update({ is_active: !user.is_active })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error toggling user status:', error);
      throw error;
    }
  }

  // Search users
  async searchUsers(query: string): Promise<UserWithOPDs[]> {
    try {
      const { data: users, error } = await supabase
        .from('users')
        .select('*')
        .or(`full_name.ilike.%${query}%,email.ilike.%${query}%,nip.ilike.%${query}%,position.ilike.%${query}%`)
        .order('full_name');

      if (error) throw error;

      // Add OPD information
      const usersWithOPDs = await Promise.all(
        (users || []).map(async (user) => {
          const userOPDs = await opdService.getUserOPDs(user.id);
          return {
            ...user,
            opds: userOPDs,
            opd_count: userOPDs.length,
          };
        })
      );

      return usersWithOPDs;
    } catch (error) {
      console.error('Error searching users:', error);
      throw error;
    }
  }

  // Get users by OPD
  async getUsersByOPD(opdId: string): Promise<UserWithOPDs[]> {
    try {
      const { data, error } = await supabase
        .from('user_opd')
        .select(`
          user:user_id (*)
        `)
        .eq('opd_id', opdId);

      if (error) throw error;

      const users = (data || [])
        .map(item => item.user)
        .filter(user => user && user.is_active) as User[];

      // Add OPD information
      const usersWithOPDs = await Promise.all(
        users.map(async (user) => {
          const userOPDs = await opdService.getUserOPDs(user.id);
          return {
            ...user,
            opds: userOPDs,
            opd_count: userOPDs.length,
          };
        })
      );

      return usersWithOPDs;
    } catch (error) {
      console.error('Error fetching users by OPD:', error);
      throw error;
    }
  }

  // Reset user password (admin only)
  async resetUserPassword(userId: string, newPassword: string): Promise<void> {
    try {
      // Verify admin access
      const { data: currentUser } = await supabase.auth.getUser();
      if (!currentUser.user) throw new Error('Unauthorized');

      const { data: currentProfile } = await supabase
        .from('users')
        .select('role')
        .eq('id', currentUser.user.id)
        .single();

      if (currentProfile?.role !== 'ADMIN') {
        throw new Error('Admin access required');
      }

      const { error } = await supabase.auth.admin.updateUserById(userId, {
        password: newPassword,
      });

      if (error) throw error;
    } catch (error) {
      console.error('Error resetting user password:', error);
      throw error;
    }
  }
}

export const userService = new UserService();
