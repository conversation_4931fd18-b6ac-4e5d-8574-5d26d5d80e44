#!/usr/bin/env node

/**
 * Database Setup Script
 * Applies schema, RLS policies, and seed data to Supabase
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase credentials in .env.local');
  console.error('Required: EXPO_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function readSQLFile(filename) {
  const filePath = path.join(__dirname, '..', 'supabase', filename);
  return fs.readFileSync(filePath, 'utf8');
}

async function executeSQLFile(filename, description) {
  console.log(`\n🔄 ${description}...`);
  
  try {
    const sql = await readSQLFile(filename);
    
    // Split SQL into individual statements (basic splitting)
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    console.log(`   Found ${statements.length} SQL statements`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.length > 0) {
        console.log(`   Executing statement ${i + 1}/${statements.length}...`);
        
        const { error } = await supabase.rpc('exec_sql', { 
          sql_query: statement + ';' 
        });
        
        if (error) {
          console.error(`   ❌ Error in statement ${i + 1}:`, error.message);
          // Continue with other statements
        } else {
          console.log(`   ✅ Statement ${i + 1} executed successfully`);
        }
      }
    }
    
    console.log(`✅ ${description} completed`);
    
  } catch (error) {
    console.error(`❌ Error in ${description}:`, error.message);
    throw error;
  }
}

async function setupDatabase() {
  console.log('🚀 Starting database setup...');
  console.log(`📍 Supabase URL: ${supabaseUrl}`);
  
  try {
    // Test connection
    console.log('\n🔍 Testing Supabase connection...');
    const { data, error } = await supabase.from('information_schema.tables').select('table_name').limit(1);
    
    if (error) {
      console.error('❌ Failed to connect to Supabase:', error.message);
      process.exit(1);
    }
    
    console.log('✅ Connected to Supabase successfully');
    
    // Apply schema
    await executeSQLFile('schema.sql', 'Creating database schema');
    
    // Apply RLS policies
    await executeSQLFile('rls_policies.sql', 'Setting up Row Level Security policies');
    
    // Insert seed data
    await executeSQLFile('seed_data.sql', 'Inserting seed data');
    
    console.log('\n🎉 Database setup completed successfully!');
    console.log('\n📋 What was created:');
    console.log('   ✅ Tables: users, opd, jenis_retribusi, user_opd, retribusi_categories, deposits, etc.');
    console.log('   ✅ RLS Policies: Role-based access control');
    console.log('   ✅ Seed Data: 6 OPDs, 3 jenis retribusi, 11 retribusi categories');
    console.log('   ✅ Indexes and triggers for performance');
    
    console.log('\n🔗 Next steps:');
    console.log('   1. Start your app: bun run dev');
    console.log('   2. Test user registration and login');
    console.log('   3. Verify data appears in Supabase dashboard');
    
  } catch (error) {
    console.error('\n💥 Database setup failed:', error.message);
    process.exit(1);
  }
}

// Create exec_sql function if it doesn't exist
async function createExecSQLFunction() {
  console.log('\n🔧 Creating exec_sql helper function...');
  
  const createFunctionSQL = `
    CREATE OR REPLACE FUNCTION exec_sql(sql_query text)
    RETURNS void
    LANGUAGE plpgsql
    SECURITY DEFINER
    AS $$
    BEGIN
      EXECUTE sql_query;
    END;
    $$;
  `;
  
  const { error } = await supabase.rpc('exec', { sql: createFunctionSQL });
  
  if (error) {
    console.log('   Note: exec_sql function creation failed (may already exist)');
  } else {
    console.log('   ✅ exec_sql function created');
  }
}

// Run setup
async function main() {
  try {
    await createExecSQLFunction();
    await setupDatabase();
  } catch (error) {
    console.error('Setup failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { setupDatabase };
