-- =============================================
-- Row Level Security (RLS) Policies - Safe Update
-- =============================================

-- Enable RLS on all tables (safe to run multiple times)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.opd ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.jenis_retribusi ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_opd ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.retribusi_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deposits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deposit_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_retribusi_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- =============================================
-- Drop existing policies if they exist
-- =============================================

-- Users table policies
DROP POLICY IF EXISTS "Users can read own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update own profile" ON public.users;
DROP POLICY IF EXISTS "Admins can read all users" ON public.users;
DROP POLICY IF EXISTS "Admins can insert users" ON public.users;
DROP POLICY IF EXISTS "Admins can update users" ON public.users;

-- OPD table policies
DROP POLICY IF EXISTS "Users can read active OPDs" ON public.opd;
DROP POLICY IF EXISTS "Admins can manage OPDs" ON public.opd;

-- Jenis Retribusi table policies
DROP POLICY IF EXISTS "Users can read active jenis retribusi" ON public.jenis_retribusi;
DROP POLICY IF EXISTS "Admins can manage jenis retribusi" ON public.jenis_retribusi;

-- User-OPD relationship policies
DROP POLICY IF EXISTS "Users can read own OPD assignments" ON public.user_opd;
DROP POLICY IF EXISTS "Admins can read all OPD assignments" ON public.user_opd;
DROP POLICY IF EXISTS "Admins can manage OPD assignments" ON public.user_opd;

-- Retribusi Categories policies
DROP POLICY IF EXISTS "Users can read active categories" ON public.retribusi_categories;
DROP POLICY IF EXISTS "Admins can manage categories" ON public.retribusi_categories;

-- Deposits policies
DROP POLICY IF EXISTS "Users can read own deposits" ON public.deposits;
DROP POLICY IF EXISTS "Users can insert own deposits" ON public.deposits;
DROP POLICY IF EXISTS "Users can update own pending deposits" ON public.deposits;
DROP POLICY IF EXISTS "Supervisors can read all deposits" ON public.deposits;
DROP POLICY IF EXISTS "Supervisors can approve deposits" ON public.deposits;

-- Deposit Attachments policies
DROP POLICY IF EXISTS "Users can read deposit attachments" ON public.deposit_attachments;
DROP POLICY IF EXISTS "Users can insert own deposit attachments" ON public.deposit_attachments;

-- Role Retribusi Assignments policies
DROP POLICY IF EXISTS "Users can read role assignments" ON public.role_retribusi_assignments;
DROP POLICY IF EXISTS "Admins can manage role assignments" ON public.role_retribusi_assignments;

-- Audit Logs policies
DROP POLICY IF EXISTS "Users can read own audit logs" ON public.audit_logs;
DROP POLICY IF EXISTS "Admins can read all audit logs" ON public.audit_logs;
DROP POLICY IF EXISTS "System can insert audit logs" ON public.audit_logs;

-- =============================================
-- Create new policies
-- =============================================

-- Users Table Policies
CREATE POLICY "Users can read own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id)
    WITH CHECK (
        auth.uid() = id AND
        role = (SELECT role FROM public.users WHERE id = auth.uid())
    );

CREATE POLICY "Admins can read all users" ON public.users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

CREATE POLICY "Admins can insert users" ON public.users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

CREATE POLICY "Admins can update users" ON public.users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- OPD Table Policies
CREATE POLICY "Users can read active OPDs" ON public.opd
    FOR SELECT USING (
        auth.role() = 'authenticated' AND is_active = true
    );

CREATE POLICY "Admins can manage OPDs" ON public.opd
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- Jenis Retribusi Table Policies
CREATE POLICY "Users can read active jenis retribusi" ON public.jenis_retribusi
    FOR SELECT USING (
        auth.role() = 'authenticated' AND is_active = true
    );

CREATE POLICY "Admins can manage jenis retribusi" ON public.jenis_retribusi
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- User-OPD Relationship Policies
CREATE POLICY "Users can read own OPD assignments" ON public.user_opd
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can read all OPD assignments" ON public.user_opd
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

CREATE POLICY "Admins can manage OPD assignments" ON public.user_opd
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- Retribusi Categories Policies
CREATE POLICY "Users can read active categories" ON public.retribusi_categories
    FOR SELECT USING (
        auth.role() = 'authenticated' AND is_active = true
    );

CREATE POLICY "Admins can manage categories" ON public.retribusi_categories
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- Deposits Table Policies
CREATE POLICY "Users can read own deposits" ON public.deposits
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own deposits" ON public.deposits
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own pending deposits" ON public.deposits
    FOR UPDATE USING (
        auth.uid() = user_id AND status = 'PENDING'
    ) WITH CHECK (
        auth.uid() = user_id AND status = 'PENDING'
    );

CREATE POLICY "Supervisors can read all deposits" ON public.deposits
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('ADMIN', 'SUPERVISOR')
        )
    );

CREATE POLICY "Supervisors can approve deposits" ON public.deposits
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role IN ('ADMIN', 'SUPERVISOR')
        )
    );

-- Deposit Attachments Policies
CREATE POLICY "Users can read deposit attachments" ON public.deposit_attachments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.deposits d
            WHERE d.id = deposit_id AND (
                d.user_id = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM public.users u
                    WHERE u.id = auth.uid() AND u.role IN ('ADMIN', 'SUPERVISOR')
                )
            )
        )
    );

CREATE POLICY "Users can insert own deposit attachments" ON public.deposit_attachments
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.deposits d
            WHERE d.id = deposit_id AND d.user_id = auth.uid()
        )
    );

-- Role Retribusi Assignments Policies
CREATE POLICY "Users can read role assignments" ON public.role_retribusi_assignments
    FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Admins can manage role assignments" ON public.role_retribusi_assignments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

-- Audit Logs Policies
CREATE POLICY "Users can read own audit logs" ON public.audit_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Admins can read all audit logs" ON public.audit_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.users 
            WHERE id = auth.uid() AND role = 'ADMIN'
        )
    );

CREATE POLICY "System can insert audit logs" ON public.audit_logs
    FOR INSERT WITH CHECK (true);
