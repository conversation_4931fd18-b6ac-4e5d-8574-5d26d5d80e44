import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
  ScrollView,
  Switch,
  ActivityIndicator,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { Save, X } from 'lucide-react-native';

import { opdService } from '../../../services/opdService';
import { useAuth } from '../../../contexts/AuthContext';
import type { OPD } from '../../../lib/supabase';

interface OPDFormData {
  name: string;
  code: string;
  description: string;
  address: string;
  phone: string;
  email: string;
  head_name: string;
  head_nip: string;
  is_active: boolean;
}

export default function EditOPDScreen() {
  const { user } = useAuth();
  const { id } = useLocalSearchParams<{ id: string }>();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [opd, setOPD] = useState<OPD | null>(null);
  const [formData, setFormData] = useState<OPDFormData>({
    name: '',
    code: '',
    description: '',
    address: '',
    phone: '',
    email: '',
    head_name: '',
    head_nip: '',
    is_active: true,
  });

  // Check if user is admin
  const isAdmin = user?.role === 'ADMIN';

  useEffect(() => {
    if (!isAdmin) {
      Alert.alert('Access Denied', 'Anda tidak memiliki akses ke halaman ini.');
      router.back();
      return;
    }

    if (!id) {
      Alert.alert('Error', 'ID OPD tidak valid');
      router.back();
      return;
    }

    loadOPD();
  }, [isAdmin, id]);

  const loadOPD = async () => {
    try {
      setLoading(true);
      const opdData = await opdService.getOPDById(id!);
      
      if (!opdData) {
        Alert.alert('Error', 'OPD tidak ditemukan');
        router.back();
        return;
      }

      setOPD(opdData);
      setFormData({
        name: opdData.name || '',
        code: opdData.code || '',
        description: opdData.description || '',
        address: opdData.address || '',
        phone: opdData.phone || '',
        email: opdData.email || '',
        head_name: opdData.head_name || '',
        head_nip: opdData.head_nip || '',
        is_active: opdData.is_active,
      });
    } catch (error) {
      console.error('Error loading OPD:', error);
      Alert.alert('Error', 'Gagal memuat data OPD');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof OPDFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      Alert.alert('Error', 'Nama OPD harus diisi');
      return false;
    }

    if (!formData.code.trim()) {
      Alert.alert('Error', 'Kode OPD harus diisi');
      return false;
    }

    // Validate email format if provided
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      Alert.alert('Error', 'Format email tidak valid');
      return false;
    }

    // Validate phone format if provided
    if (formData.phone && !/^[0-9+\-\s()]+$/.test(formData.phone)) {
      Alert.alert('Error', 'Format nomor telepon tidak valid');
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      setSaving(true);

      const updateData = {
        name: formData.name.trim(),
        code: formData.code.trim().toUpperCase(),
        description: formData.description.trim() || null,
        address: formData.address.trim() || null,
        phone: formData.phone.trim() || null,
        email: formData.email.trim() || null,
        head_name: formData.head_name.trim() || null,
        head_nip: formData.head_nip.trim() || null,
        is_active: formData.is_active,
      };

      await opdService.updateOPD(id!, updateData);

      Alert.alert(
        'Berhasil',
        'OPD berhasil diperbarui',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Error updating OPD:', error);
      
      let errorMessage = 'Gagal memperbarui OPD';
      if (error.message?.includes('duplicate key')) {
        if (error.message.includes('name')) {
          errorMessage = 'Nama OPD sudah digunakan';
        } else if (error.message.includes('code')) {
          errorMessage = 'Kode OPD sudah digunakan';
        }
      }
      
      Alert.alert('Error', errorMessage);
    } finally {
      setSaving(false);
    }
  };

  if (!isAdmin) {
    return null;
  }

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text style={styles.loadingText}>Memuat data OPD...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => router.back()}
        >
          <X size={24} color="#6B7280" />
        </TouchableOpacity>
        <Text style={styles.title}>Edit OPD</Text>
        <TouchableOpacity
          style={[styles.saveButton, saving && styles.disabledButton]}
          onPress={handleSave}
          disabled={saving}
        >
          <Save size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Dasar</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nama OPD *</Text>
            <TextInput
              style={styles.input}
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
              placeholder="Contoh: Dinas Pendapatan Daerah"
              multiline
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Kode OPD *</Text>
            <TextInput
              style={styles.input}
              value={formData.code}
              onChangeText={(value) => handleInputChange('code', value.toUpperCase())}
              placeholder="Contoh: DISPENDA"
              autoCapitalize="characters"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Deskripsi</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              placeholder="Deskripsi singkat tentang OPD"
              multiline
              numberOfLines={3}
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Kontak</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Alamat</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={formData.address}
              onChangeText={(value) => handleInputChange('address', value)}
              placeholder="Alamat lengkap OPD"
              multiline
              numberOfLines={2}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nomor Telepon</Text>
            <TextInput
              style={styles.input}
              value={formData.phone}
              onChangeText={(value) => handleInputChange('phone', value)}
              placeholder="Contoh: (021) 1234567"
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={styles.input}
              value={formData.email}
              onChangeText={(value) => handleInputChange('email', value)}
              placeholder="Contoh: <EMAIL>"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Kepala Dinas</Text>
          
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nama Kepala Dinas</Text>
            <TextInput
              style={styles.input}
              value={formData.head_name}
              onChangeText={(value) => handleInputChange('head_name', value)}
              placeholder="Nama lengkap kepala dinas"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>NIP Kepala Dinas</Text>
            <TextInput
              style={styles.input}
              value={formData.head_nip}
              onChangeText={(value) => handleInputChange('head_nip', value)}
              placeholder="NIP kepala dinas"
              keyboardType="numeric"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Status</Text>
          
          <View style={styles.switchRow}>
            <Text style={styles.label}>OPD Aktif</Text>
            <Switch
              value={formData.is_active}
              onValueChange={(value) => handleInputChange('is_active', value)}
              trackColor={{ false: '#D1D5DB', true: '#3B82F6' }}
              thumbColor={formData.is_active ? '#FFFFFF' : '#F3F4F6'}
            />
          </View>
        </View>

        <View style={styles.bottomPadding} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  cancelButton: {
    padding: 8,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  saveButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  disabledButton: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 20,
    marginTop: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#111827',
    backgroundColor: '#FFFFFF',
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bottomPadding: {
    height: 40,
  },
});
