import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { AuthService, type AuthUser } from '../services/authService';
import type { User } from '@supabase/supabase-js';

interface AuthContextType {
  user: User | null;
  profile: AuthUser | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (updates: {
    full_name?: string;
    department?: string;
    position?: string;
    phone?: string;
    address?: string;
  }) => Promise<void>;
  changePassword: (newPassword: string) => Promise<void>;
  isAdmin: boolean;
  isUser: boolean;
  isSupervisor: boolean;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      if (session?.user) {
        loadProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        
        setUser(session?.user ?? null);
        
        if (session?.user) {
          await loadProfile(session.user.id);
        } else {
          setProfile(null);
          setLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const loadProfile = async (userId: string) => {
    try {
      setLoading(true);
      const { data } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();
      
      setProfile(data);
    } catch (error) {
      console.error('Error loading profile:', error);
      setProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);
      const result = await AuthService.signIn(email, password);
      setUser(result.user);
      setProfile(result.profile);
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      await AuthService.signOut();
      setUser(null);
      setProfile(null);
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: {
    full_name?: string;
    department?: string;
    position?: string;
    phone?: string;
    address?: string;
  }) => {
    if (!user) throw new Error('No user logged in');
    
    try {
      const updatedProfile = await AuthService.updateProfile(user.id, updates);
      setProfile(updatedProfile);
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  };

  const changePassword = async (newPassword: string) => {
    try {
      await AuthService.changePassword(newPassword);
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  };

  const refreshProfile = async () => {
    if (!user) return;
    await loadProfile(user.id);
  };

  // Role checks
  const isAdmin = profile?.role === 'ADMIN';
  const isUser = profile?.role === 'USER';
  const isSupervisor = profile?.role === 'SUPERVISOR';

  const value: AuthContextType = {
    user,
    profile,
    loading,
    signIn,
    signOut,
    updateProfile,
    changePassword,
    isAdmin,
    isUser,
    isSupervisor,
    refreshProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Higher-order component untuk role-based access
export function withAuth<T extends object>(
  Component: React.ComponentType<T>,
  requiredRole?: 'ADMIN' | 'USER' | 'SUPERVISOR'
) {
  return function AuthenticatedComponent(props: T) {
    const { user, profile, loading } = useAuth();

    if (loading) {
      return null; // atau loading component
    }

    if (!user || !profile) {
      return null; // akan redirect ke login di _layout
    }

    if (requiredRole && profile.role !== requiredRole) {
      return null; // atau unauthorized component
    }

    return <Component {...props} />;
  };
}

// Hook untuk permission checking
export function usePermissions() {
  const { profile } = useAuth();

  const can = {
    viewAllDeposits: profile?.role === 'ADMIN',
    manageUsers: profile?.role === 'ADMIN',
    approveDeposits: profile?.role === 'ADMIN' || profile?.role === 'SUPERVISOR',
    createDeposits: profile?.role !== undefined,
    editOwnDeposits: profile?.role !== undefined,
    deleteOwnDeposits: profile?.role === 'ADMIN' || profile?.role === 'USER',
    viewReports: profile?.role !== undefined,
    exportData: profile?.role === 'ADMIN',
    manageSettings: profile?.role === 'ADMIN',
  };

  return can;
}
