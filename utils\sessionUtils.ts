/**
 * Session management utilities for development and testing
 */

export const SessionUtils = {
  /**
   * Force clear all session data (for development/testing)
   */
  forceClearSession: () => {
    if (typeof window !== 'undefined') {
      console.log('🧹 Force clearing all session data...');
      
      // Clear localStorage
      localStorage.removeItem('mock_user_session');
      
      // Clear sessionStorage
      sessionStorage.removeItem('mock_user_session');
      
      // Clear any other auth-related storage
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_data');
      sessionStorage.removeItem('auth_token');
      sessionStorage.removeItem('user_data');
      
      console.log('✅ All session data cleared');
    }
  },

  /**
   * Check if there's any stored session
   */
  hasStoredSession: (): boolean => {
    if (typeof window !== 'undefined') {
      return !!(
        localStorage.getItem('mock_user_session') ||
        sessionStorage.getItem('mock_user_session')
      );
    }
    return false;
  },

  /**
   * Get session info for debugging
   */
  getSessionInfo: () => {
    if (typeof window !== 'undefined') {
      const localStorage_session = localStorage.getItem('mock_user_session');
      const sessionStorage_session = sessionStorage.getItem('mock_user_session');
      
      return {
        hasLocalStorage: !!localStorage_session,
        hasSessionStorage: !!sessionStorage_session,
        localStorageData: localStorage_session ? JSON.parse(localStorage_session) : null,
        sessionStorageData: sessionStorage_session ? JSON.parse(sessionStorage_session) : null,
      };
    }
    return null;
  },

  /**
   * Clear expired sessions
   */
  clearExpiredSessions: () => {
    if (typeof window !== 'undefined') {
      const storedSession = localStorage.getItem('mock_user_session');
      if (storedSession) {
        try {
          const sessionData = JSON.parse(storedSession);
          const sessionAge = Date.now() - sessionData.timestamp;
          const maxAge = 24 * 60 * 60 * 1000; // 24 hours
          
          if (sessionAge >= maxAge) {
            console.log('⏰ Clearing expired session...');
            localStorage.removeItem('mock_user_session');
            return true;
          }
        } catch (error) {
          console.error('❌ Error checking session expiry:', error);
          localStorage.removeItem('mock_user_session');
          return true;
        }
      }
    }
    return false;
  },
};
