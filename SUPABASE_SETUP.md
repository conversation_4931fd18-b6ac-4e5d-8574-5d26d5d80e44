# 🚀 Supabase Setup Guide - Report Retribusi

## 📋 Prerequisites

1. **Supabase Account**: Sign up at [supabase.com](https://supabase.com)
2. **Project Created**: Create a new Supabase project

## 🔧 Step-by-Step Setup

### **1. Create Supabase Project**

1. **Login** to Supabase Dashboard
2. **Click "New Project"**
3. **Fill Project Details**:
   - Organization: Select your organization
   - Name: `report-retribusi`
   - Database Password: Generate strong password (save this!)
   - Region: `Southeast Asia (Singapore)` (for best performance)
4. **Click "Create new project"**
5. **Wait** for project initialization (~2 minutes)

### **2. Get Project Credentials**

1. **Go to Project Dashboard**
2. **Navigate to Settings > API**
3. **Copy the following values**:
   - **Project URL**: `https://your-project-id.supabase.co`
   - **Anon Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (keep secret!)

### **3. Configure Environment Variables**

1. **Copy environment template**:
   ```bash
   cp .env.example .env.local
   ```

2. **Update `.env.local`** with your credentials:
   ```env
   EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
   EXPO_PUBLIC_FORCE_MOCK_MODE=false
   ```

### **4. Setup Database Schema**

1. **Go to Supabase Dashboard > SQL Editor**
2. **Run the following SQL files in order**:

#### **A. Create Schema** (`supabase/schema.sql`)
```sql
-- Copy and paste the entire content of supabase/schema.sql
-- This creates all tables, indexes, and triggers
```

#### **B. Setup RLS Policies** (`supabase/rls_policies.sql`)
```sql
-- Copy and paste the entire content of supabase/rls_policies.sql
-- This sets up Row Level Security for data protection
```

#### **C. Insert Seed Data** (`supabase/seed_data.sql`)
```sql
-- Copy and paste the entire content of supabase/seed_data.sql
-- This creates initial categories and helper functions
```

### **5. Verify Setup**

1. **Check Tables Created**:
   - Go to **Table Editor**
   - Verify these tables exist:
     - `users`
     - `retribusi_categories`
     - `deposits`
     - `deposit_attachments`
     - `role_retribusi_assignments`
     - `audit_logs`

2. **Check Seed Data**:
   - Open `retribusi_categories` table
   - Should see 8 categories (Parkir, Pasar, Terminal, etc.)

3. **Test Authentication**:
   - Go to **Authentication > Users**
   - Should be empty initially (users will be created when they register)

### **6. Test Connection**

1. **Start the app**:
   ```bash
   bun run dev
   ```

2. **Check console logs**:
   - Should see: `Using real Supabase client`
   - No mock mode messages

3. **Test registration**:
   - Try creating a new user
   - Check if user appears in Supabase Dashboard > Authentication > Users

## 🔐 Security Configuration

### **Row Level Security (RLS)**

All tables have RLS enabled with these policies:

- **Users**: Can read/update own profile, admins can manage all users
- **Deposits**: Users see own deposits, supervisors/admins see all
- **Categories**: All authenticated users can read, admins can manage
- **Attachments**: Access based on deposit ownership

### **Role-based Access**

- **USER**: Limited categories (Parkir, Kebersihan)
- **SUPERVISOR**: More categories (Parkir, Pasar, Terminal, Kebersihan)
- **ADMIN**: All categories and user management

## 🧪 Testing

### **Create Test Users**

1. **Register via app** or **manually insert**:
   ```sql
   -- Insert test admin (after creating auth user)
   INSERT INTO auth.users (email, encrypted_password, email_confirmed_at)
   VALUES ('<EMAIL>', crypt('admin123', gen_salt('bf')), NOW());
   ```

2. **Assign roles** in `users` table

### **Test Functionality**

- ✅ User registration/login
- ✅ Role-based category access
- ✅ Deposit creation/editing
- ✅ Admin user management
- ✅ Data persistence

## 🚨 Troubleshooting

### **Common Issues**

1. **"Invalid API key"**:
   - Check `.env.local` file exists
   - Verify EXPO_PUBLIC_SUPABASE_URL and EXPO_PUBLIC_SUPABASE_ANON_KEY
   - Restart development server

2. **"Permission denied"**:
   - Check RLS policies are applied
   - Verify user has correct role
   - Check table permissions

3. **"Table doesn't exist"**:
   - Run schema.sql in SQL Editor
   - Check all tables created successfully

4. **Mock mode still active**:
   - Set `EXPO_PUBLIC_FORCE_MOCK_MODE=false`
   - Restart development server
   - Check console logs

### **Reset Database**

If you need to start over:

1. **Go to Settings > General**
2. **Scroll to "Danger Zone"**
3. **Click "Reset database password"** or recreate project
4. **Re-run all SQL files**

## 📞 Support

If you encounter issues:

1. **Check Supabase logs**: Dashboard > Logs
2. **Check browser console**: Look for error messages
3. **Verify environment variables**: Restart after changes
4. **Test with mock mode**: Set `EXPO_PUBLIC_FORCE_MOCK_MODE=true` temporarily

## 🎉 Success!

Once setup is complete, you should have:

- ✅ **Real Supabase backend** with proper schema
- ✅ **Authentication system** with role-based access
- ✅ **Data persistence** across app restarts
- ✅ **Admin user management** functionality
- ✅ **Secure RLS policies** protecting data
