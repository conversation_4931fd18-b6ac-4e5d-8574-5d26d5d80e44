# Deployment Guide - Report Retribusi

Panduan lengkap untuk deployment aplikasi Report Retribusi ke berbagai platform.

## 🌐 Web Deployment

### 1. Build untuk Web

```bash
# Build aplikasi untuk web
bun run build:web

# Output akan tersimpan di folder dist/
```

### 2. Deploy ke Vercel

```bash
# Install Vercel CLI
bun add -g vercel

# Login ke Vercel
vercel login

# Deploy
vercel --prod

# Atau menggunakan GitHub integration
# Push ke repository, Vercel akan auto-deploy
```

**vercel.json configuration:**
```json
{
  "buildCommand": "bun run build:web",
  "outputDirectory": "dist",
  "framework": "react",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

### 3. Deploy ke Netlify

```bash
# Install Netlify CLI
bun add -g netlify-cli

# Login ke Netlify
netlify login

# Deploy
netlify deploy --prod --dir=dist
```

**netlify.toml configuration:**
```toml
[build]
  command = "bun run build:web"
  publish = "dist"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 4. Deploy ke GitHub Pages

```yaml
# .github/workflows/deploy.yml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest
    
    - name: Install dependencies
      run: bun install
    
    - name: Build
      run: bun run build:web
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

## 📱 Mobile App Deployment

### 1. Android Deployment

#### Persiapan

```bash
# Install EAS CLI
bun add -g @expo/eas-cli

# Login ke Expo
eas login

# Konfigurasi project
eas build:configure
```

#### Build APK untuk Testing

```bash
# Build APK
eas build --platform android --profile preview

# Download APK setelah build selesai
```

#### Build AAB untuk Google Play Store

```bash
# Build untuk production
eas build --platform android --profile production

# Submit ke Google Play Store
eas submit --platform android
```

**eas.json configuration:**
```json
{
  "cli": {
    "version": ">= 5.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "android": {
        "buildType": "apk"
      }
    },
    "production": {
      "android": {
        "buildType": "aab"
      }
    }
  },
  "submit": {
    "production": {
      "android": {
        "serviceAccountKeyPath": "./google-service-account.json",
        "track": "internal"
      }
    }
  }
}
```

### 2. iOS Deployment

#### Persiapan

```bash
# Pastikan memiliki Apple Developer Account
# Install Xcode di macOS

# Build untuk iOS
eas build --platform ios --profile production
```

#### Submit ke App Store

```bash
# Submit ke App Store
eas submit --platform ios
```

**app.json configuration untuk iOS:**
```json
{
  "expo": {
    "ios": {
      "bundleIdentifier": "com.yourcompany.reportretribusi",
      "buildNumber": "1.0.0",
      "supportsTablet": true
    }
  }
}
```

## 🔧 Environment Configuration

### 1. Environment Variables

**app.config.js:**
```javascript
export default {
  expo: {
    name: process.env.EXPO_PUBLIC_APP_NAME || "Report Retribusi",
    slug: "report-retribusi",
    version: "1.0.0",
    extra: {
      apiUrl: process.env.EXPO_PUBLIC_API_URL,
      environment: process.env.EXPO_PUBLIC_ENVIRONMENT || "development",
    },
  },
};
```

### 2. Environment Files

**.env.development:**
```
EXPO_PUBLIC_API_URL=http://localhost:3000/api/v1
EXPO_PUBLIC_ENVIRONMENT=development
EXPO_PUBLIC_APP_NAME=Report Retribusi (Dev)
```

**.env.production:**
```
EXPO_PUBLIC_API_URL=https://api.report-retribusi.com/v1
EXPO_PUBLIC_ENVIRONMENT=production
EXPO_PUBLIC_APP_NAME=Report Retribusi
```

### 3. Menggunakan Environment Variables

```typescript
// config/env.ts
import Constants from 'expo-constants';

export const config = {
  apiUrl: Constants.expoConfig?.extra?.apiUrl,
  environment: Constants.expoConfig?.extra?.environment,
  appName: Constants.expoConfig?.extra?.appName,
};
```

## 🚀 CI/CD Pipeline

### 1. GitHub Actions untuk Web

```yaml
# .github/workflows/web-deploy.yml
name: Web Deployment

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
    
    - name: Install dependencies
      run: bun install
    
    - name: Run tests
      run: bun test
    
    - name: Run linting
      run: bun run lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
    
    - name: Install dependencies
      run: bun install
    
    - name: Build
      run: bun run build:web
      env:
        EXPO_PUBLIC_API_URL: ${{ secrets.API_URL }}
        EXPO_PUBLIC_ENVIRONMENT: production
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
```

### 2. GitHub Actions untuk Mobile

```yaml
# .github/workflows/mobile-build.yml
name: Mobile Build

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
    
    - name: Setup EAS
      uses: expo/expo-github-action@v8
      with:
        eas-version: latest
        token: ${{ secrets.EXPO_TOKEN }}
    
    - name: Install dependencies
      run: bun install
    
    - name: Build Android
      run: eas build --platform android --non-interactive
    
    - name: Build iOS
      run: eas build --platform ios --non-interactive
```

## 📊 Monitoring dan Analytics

### 1. Sentry Integration

```bash
# Install Sentry
bun add @sentry/react-native

# Setup Sentry
npx @sentry/wizard -i reactNative -p ios android
```

**Sentry Configuration:**
```typescript
// app/_layout.tsx
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'YOUR_SENTRY_DSN',
  environment: process.env.EXPO_PUBLIC_ENVIRONMENT,
});

export default Sentry.wrap(RootLayout);
```

### 2. Analytics dengan Expo Analytics

```typescript
// utils/analytics.ts
import { Analytics } from 'expo-analytics';

const analytics = new Analytics('YOUR_TRACKING_ID');

export const trackEvent = (event: string, properties?: object) => {
  analytics.event(event, properties);
};

export const trackScreen = (screenName: string) => {
  analytics.screen(screenName);
};
```

## 🔒 Security Considerations

### 1. API Keys dan Secrets

```bash
# Jangan commit secrets ke repository
# Gunakan environment variables atau Expo Secrets

# Set secret menggunakan EAS
eas secret:create --scope project --name API_KEY --value your-api-key
```

### 2. Code Obfuscation

```json
// app.json
{
  "expo": {
    "hooks": {
      "postPublish": [
        {
          "file": "sentry-expo/upload-sourcemaps",
          "config": {
            "organization": "your-org",
            "project": "your-project"
          }
        }
      ]
    }
  }
}
```

## 📋 Pre-deployment Checklist

### Web Deployment
- [ ] Build berhasil tanpa error
- [ ] All tests passing
- [ ] Environment variables configured
- [ ] Performance optimization
- [ ] SEO meta tags
- [ ] PWA configuration
- [ ] Analytics setup

### Mobile Deployment
- [ ] App icons dan splash screen
- [ ] App store metadata
- [ ] Privacy policy dan terms
- [ ] Push notification setup
- [ ] Deep linking configuration
- [ ] App signing certificates
- [ ] Store listing optimization

## 🔄 Post-deployment

### 1. Monitoring

```bash
# Monitor aplikasi menggunakan:
# - Sentry untuk error tracking
# - Analytics untuk user behavior
# - Performance monitoring
```

### 2. Updates

```bash
# Over-the-air updates dengan Expo Updates
eas update --branch production --message "Bug fixes"
```

### 3. Rollback

```bash
# Rollback jika ada masalah
eas update --branch production --message "Rollback to previous version"
```

---

**Deployment Success! 🚀**
