# Report Retribusi

Aplikasi mobile untuk pelaporan retribusi menggunakan React Native dengan Expo.

## 📱 Tentang Aplikasi

Report Retribusi adalah aplikasi mobile yang dirancang untuk memudahkan proses pelaporan dan pengelolaan retribusi. Aplikasi ini dibangun menggunakan teknologi modern React Native dengan framework Expo untuk pengembangan yang cepat dan efisien.

## 🚀 Teknologi yang Digunakan

- **React Native** 0.79.2 - Framework untuk pengembangan aplikasi mobile
- **Expo** 53.0.9 - Platform untuk pengembangan React Native
- **Expo Router** 5.0.7 - Routing berbasis file system
- **TypeScript** - Type safety dan developer experience yang lebih baik
- **React Navigation** - Navigasi antar screen
- **Expo Camera** - Fitur kamera untuk dokumentasi
- **Lucide React Native** - Icon library

## 📋 Prasyarat

Sebelum menjalankan aplikasi, pastikan Anda telah menginstall:

- [Node.js](https://nodejs.org/) (versi 18 atau lebih baru)
- [Bun](https://bun.sh/) - Package manager yang digunakan
- [Expo CLI](https://docs.expo.dev/get-started/installation/)
- [Expo Go](https://expo.dev/client) app di device mobile (untuk testing)

## 🛠️ Instalasi

1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd report-retribusi
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Jalankan development server**
   ```bash
   bun run dev
   ```

## 📱 Menjalankan Aplikasi

### Development Mode

```bash
# Jalankan development server
bun run dev

# Atau dengan opsi spesifik
EXPO_NO_TELEMETRY=1 expo start
```

Setelah server berjalan, Anda akan melihat QR code di terminal. Anda dapat:

- **Web**: Buka http://localhost:8081 di browser
- **Mobile**: Scan QR code dengan Expo Go app
- **Android Emulator**: Tekan `a` di terminal
- **iOS Simulator**: Tekan `i` di terminal (hanya di macOS)

### Build untuk Production

```bash
# Build untuk web
bun run build:web
```

## 📁 Struktur Project

```
report-retribusi/
├── app/                    # Aplikasi utama (Expo Router)
│   ├── (tabs)/            # Tab navigation screens
│   ├── auth/              # Authentication screens
│   ├── _layout.tsx        # Root layout
│   ├── index.tsx          # Home screen
│   └── +not-found.tsx     # 404 screen
├── assets/                # Asset statis (gambar, font, dll)
│   └── images/
├── components/            # Komponen reusable
│   └── StatusBar.tsx
├── data/                  # Data dan mock data
│   └── mockData.ts
├── hooks/                 # Custom hooks
│   └── useFrameworkReady.ts
├── package.json           # Dependencies dan scripts
├── tsconfig.json          # TypeScript configuration
└── expo-env.d.ts         # Expo type definitions
```

## 🎯 Fitur Utama

- **Dashboard Retribusi** - Overview data retribusi
- **Pelaporan** - Form untuk melaporkan retribusi
- **Kamera Integration** - Dokumentasi foto untuk laporan
- **Navigation** - Tab-based navigation yang intuitif
- **Authentication** - Sistem login dan registrasi
- **Responsive Design** - Mendukung berbagai ukuran layar

## 🔧 Scripts yang Tersedia

```bash
# Development
bun run dev              # Jalankan development server

# Build
bun run build:web        # Build untuk web platform

# Linting
bun run lint            # Jalankan ESLint untuk code quality
```

## 📦 Dependencies Utama

### Production Dependencies
- `expo` - Platform pengembangan
- `react` & `react-native` - Core framework
- `expo-router` - File-based routing
- `@react-navigation/*` - Navigation library
- `expo-camera` - Camera functionality
- `lucide-react-native` - Icons
- `expo-font` - Custom fonts

### Development Dependencies
- `typescript` - Type checking
- `@babel/core` - JavaScript compiler
- `@types/react` - TypeScript definitions

## 🎨 Styling dan UI

Aplikasi menggunakan:
- **React Native StyleSheet** untuk styling
- **Expo Linear Gradient** untuk gradient effects
- **Expo Blur** untuk blur effects
- **Poppins Font** dari Google Fonts
- **Lucide Icons** untuk konsistensi icon

## 📱 Platform Support

- ✅ **iOS** - Native iOS app
- ✅ **Android** - Native Android app  
- ✅ **Web** - Progressive Web App (PWA)

## 🔄 Development Workflow

1. **Start development server**: `bun run dev`
2. **Make changes** di code editor
3. **Hot reload** akan otomatis refresh aplikasi
4. **Test** di berbagai platform (web, mobile)
5. **Commit changes** setelah testing

## 🐛 Troubleshooting

### Common Issues

**Metro bundler error**
```bash
# Clear cache dan restart
expo start --clear
```

**Package compatibility issues**
```bash
# Update semua packages
bun update
```

**TypeScript errors**
```bash
# Check TypeScript configuration
npx tsc --noEmit
```

## 📚 Resources

- [Expo Documentation](https://docs.expo.dev/)
- [React Native Documentation](https://reactnative.dev/)
- [Expo Router Documentation](https://expo.github.io/router/)
- [React Navigation](https://reactnavigation.org/)

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

Project ini menggunakan lisensi MIT. Lihat file `LICENSE` untuk detail.

---

**Dibuat dengan ❤️ menggunakan React Native & Expo**
