# Supabase Configuration
# Copy this file to .env.local and fill in your actual values

# Supabase Project URL
# Get this from your Supabase project dashboard
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co

# Supabase Anon Key
# Get this from your Supabase project dashboard > Settings > API
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# Supabase Service Role Key (for admin operations)
# Get this from your Supabase project dashboard > Settings > API
# Keep this secret and never expose in client-side code
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# App Configuration
EXPO_PUBLIC_APP_NAME=Report Retribusi
EXPO_PUBLIC_ENVIRONMENT=development

# Optional: Analytics and Monitoring
# EXPO_PUBLIC_SENTRY_DSN=your-sentry-dsn
# EXPO_PUBLIC_ANALYTICS_ID=your-analytics-id
