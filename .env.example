# Supabase Configuration
# Copy this file to .env.local and fill in your actual values

# Supabase Project URL
# Get this from your Supabase project dashboard
EXPO_PUBLIC_SUPABASE_URL=https://trmfcjqhfhbsesvqbnsi.supabase.co

# Supabase Anon Key
# Get this from your Supabase project dashboard > Settings > API
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRybWZjanFoZmhic2VzdnFibnNpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MzU0NDMsImV4cCI6MjA2NDQxMTQ0M30.NKbpuLsCkBDl1rCyWoiCO9oVkEu7AT9xdXBWwMsKUuw

# Supabase Service Role Key (for admin operations)
# Get this from your Supabase project dashboard > Settings > API
# Keep this secret and never expose in client-side code
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRybWZjanFoZmhic2VzdnFibnNpIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODgzNTQ0MywiZXhwIjoyMDY0NDExNDQzfQ.ExCbqp0hMY8MW8CHQDzyneGZ9cdIx9xYXtAM0uetOKU

# App Configuration
EXPO_PUBLIC_APP_NAME=Report Retribusi
EXPO_PUBLIC_ENVIRONMENT=development

# Optional: Analytics and Monitoring
# EXPO_PUBLIC_SENTRY_DSN=your-sentry-dsn
# EXPO_PUBLIC_ANALYTICS_ID=your-analytics-id
