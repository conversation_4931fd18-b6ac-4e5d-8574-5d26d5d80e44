-- =============================================
-- Seed Data for Report Retribusi - Safe Insert
-- =============================================

-- =============================================
-- 1. Insert OPD (Organisasi Perangkat Daerah) - Safe
-- =============================================
INSERT INTO public.opd (name, code, description) VALUES
('Dinas Pendapatan Daerah', 'DISPENDA', 'Dinas yang mengelola pendapatan asli daerah'),
('Dinas Perhubungan', 'DISHUB', 'Dinas yang mengelola transportasi dan perhubungan'),
('<PERSON>as Ling<PERSON>n Hidup', 'DLH', 'Dinas yang mengelola lingkungan hidup dan kebersihan'),
('Dinas Perdagangan', 'DISDAG', 'Dinas yang mengelola perdagangan dan pasar'),
('Dinas Pekerjaan Umum', 'DPUPR', 'Dinas yang mengelola infrastruktur dan pekerjaan umum'),
('Dinas Penanaman Modal dan PTSP', 'DPMPTSP', 'Dinas yang mengelola perizinan dan penanaman modal')
ON CONFLICT (name) DO NOTHING;

-- =============================================
-- 2. Insert Jenis Retribusi - Safe
-- =============================================
INSERT INTO public.jenis_retribusi (name, code, base_account_number, description) VALUES
('Jasa Umum', 'JU', '*********', 'Retribusi atas jasa yang disediakan atau diberikan oleh Pemerintah Daerah untuk tujuan kepentingan dan kemanfaatan umum'),
('Jasa Usaha', 'JUS', '*********', 'Retribusi atas jasa yang disediakan oleh Pemerintah Daerah dengan menganut prinsip komersial'),
('Perizinan Tertentu', 'PT', '*********', 'Retribusi atas kegiatan tertentu Pemerintah Daerah dalam rangka pemberian izin kepada orang pribadi atau badan')
ON CONFLICT (code) DO NOTHING;

-- =============================================
-- 3. Insert Retribusi Categories - Safe
-- =============================================
DO $$
DECLARE
    dispenda_id UUID;
    dishub_id UUID;
    dlh_id UUID;
    disdag_id UUID;
    dpupr_id UUID;
    dpmptsp_id UUID;
    jasa_umum_id UUID;
    jasa_usaha_id UUID;
    perizinan_id UUID;
BEGIN
    -- Get OPD IDs
    SELECT id INTO dispenda_id FROM public.opd WHERE code = 'DISPENDA';
    SELECT id INTO dishub_id FROM public.opd WHERE code = 'DISHUB';
    SELECT id INTO dlh_id FROM public.opd WHERE code = 'DLH';
    SELECT id INTO disdag_id FROM public.opd WHERE code = 'DISDAG';
    SELECT id INTO dpupr_id FROM public.opd WHERE code = 'DPUPR';
    SELECT id INTO dpmptsp_id FROM public.opd WHERE code = 'DPMPTSP';
    
    -- Get Jenis Retribusi IDs
    SELECT id INTO jasa_umum_id FROM public.jenis_retribusi WHERE code = 'JU';
    SELECT id INTO jasa_usaha_id FROM public.jenis_retribusi WHERE code = 'JUS';
    SELECT id INTO perizinan_id FROM public.jenis_retribusi WHERE code = 'PT';
    
    -- Insert Retribusi Categories (only if they don't exist)
    INSERT INTO public.retribusi_categories (nomor_rekening, nama_retribusi, opd_id, jenis_retribusi_id, description, base_tariff, icon, color) 
    SELECT * FROM (VALUES
        -- Jasa Umum - Dishub
        ('*********.001.01', 'Retribusi Parkir Tepi Jalan Umum', dishub_id, jasa_umum_id, 'Retribusi parkir kendaraan di tepi jalan umum', 2000.00, 'car', '#F59E0B'),
        ('*********.001.02', 'Retribusi Parkir Wisata', dishub_id, jasa_umum_id, 'Retribusi parkir kendaraan di area wisata', 3000.00, 'car', '#F59E0B'),
        ('*********.002.01', 'Retribusi Terminal Angkutan Umum', dishub_id, jasa_umum_id, 'Retribusi terminal angkutan umum', 5000.00, 'bus', '#6366F1'),
        
        -- Jasa Umum - Disdag
        ('*********.003.01', 'Retribusi Pasar Tradisional', disdag_id, jasa_umum_id, 'Retribusi pedagang pasar tradisional', 5000.00, 'store', '#10B981'),
        ('*********.003.02', 'Retribusi Kios Pasar', disdag_id, jasa_umum_id, 'Retribusi penyewaan kios pasar', 10000.00, 'store', '#10B981'),
        
        -- Jasa Umum - DLH
        ('*********.004.01', 'Retribusi Pelayanan Kebersihan', dlh_id, jasa_umum_id, 'Retribusi pelayanan kebersihan dan persampahan', 15000.00, 'trash-2', '#EF4444'),
        
        -- Jasa Usaha - DPUPR
        ('*********.001.01', 'Retribusi Air Bersih', dpupr_id, jasa_usaha_id, 'Retribusi pelayanan air bersih', 25000.00, 'droplets', '#06B6D4'),
        ('*********.002.01', 'Retribusi Penerangan Jalan', dpupr_id, jasa_usaha_id, 'Retribusi penerangan jalan umum', 30000.00, 'lightbulb', '#FBBF24'),
        
        -- Perizinan Tertentu - DPMPTSP
        ('*********.001.01', 'Retribusi Izin Mendirikan Bangunan', dpmptsp_id, perizinan_id, 'Retribusi pengurusan IMB', 100000.00, 'file-text', '#8B5CF6'),
        ('*********.002.01', 'Retribusi Izin Usaha', dpmptsp_id, perizinan_id, 'Retribusi pengurusan izin usaha', 75000.00, 'briefcase', '#8B5CF6'),
        ('*********.003.01', 'Retribusi Izin Reklame', dpmptsp_id, perizinan_id, 'Retribusi pemasangan reklame', 150000.00, 'megaphone', '#F97316')
    ) AS v(nomor_rekening, nama_retribusi, opd_id, jenis_retribusi_id, description, base_tariff, icon, color)
    WHERE NOT EXISTS (
        SELECT 1 FROM public.retribusi_categories WHERE nomor_rekening = v.nomor_rekening
    );
    
END $$;

-- =============================================
-- 4. Insert Role Retribusi Assignments - Safe
-- =============================================

-- Clear existing assignments first
DELETE FROM public.role_retribusi_assignments;

-- USER role assignments (limited access - only parkir and kebersihan)
INSERT INTO public.role_retribusi_assignments (user_role, retribusi_category_id, can_create, can_edit, can_delete) 
SELECT 'USER', id, true, true, false FROM public.retribusi_categories 
WHERE nama_retribusi LIKE '%Parkir%' OR nama_retribusi LIKE '%Kebersihan%';

-- SUPERVISOR role assignments (more access)
INSERT INTO public.role_retribusi_assignments (user_role, retribusi_category_id, can_create, can_edit, can_delete) 
SELECT 'SUPERVISOR', id, true, true, true FROM public.retribusi_categories 
WHERE nama_retribusi LIKE '%Parkir%' OR nama_retribusi LIKE '%Pasar%' OR nama_retribusi LIKE '%Terminal%' OR nama_retribusi LIKE '%Kebersihan%';

-- ADMIN role assignments (full access to all categories)
INSERT INTO public.role_retribusi_assignments (user_role, retribusi_category_id, can_create, can_edit, can_delete) 
SELECT 'ADMIN', id, true, true, true FROM public.retribusi_categories;

-- =============================================
-- 5. Create Function to Handle New User Registration
-- =============================================
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'role', 'USER')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 6. Create Trigger for New User Registration (Safe)
-- =============================================
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- =============================================
-- 7. Create Function for Audit Logging
-- =============================================
CREATE OR REPLACE FUNCTION public.log_audit()
RETURNS TRIGGER AS $$
DECLARE
    user_id_val UUID;
BEGIN
    -- Get current user ID
    user_id_val := auth.uid();
    
    -- Insert audit log
    INSERT INTO public.audit_logs (
        user_id,
        action,
        table_name,
        record_id,
        old_values,
        new_values
    ) VALUES (
        user_id_val,
        TG_OP,
        TG_TABLE_NAME,
        CASE 
            WHEN TG_OP = 'DELETE' THEN OLD.id
            ELSE NEW.id
        END,
        CASE 
            WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD)
            WHEN TG_OP = 'UPDATE' THEN to_jsonb(OLD)
            ELSE NULL
        END,
        CASE 
            WHEN TG_OP = 'DELETE' THEN NULL
            ELSE to_jsonb(NEW)
        END
    );
    
    RETURN CASE 
        WHEN TG_OP = 'DELETE' THEN OLD
        ELSE NEW
    END;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- 8. Create Audit Triggers (Safe)
-- =============================================
DROP TRIGGER IF EXISTS audit_users_trigger ON public.users;
CREATE TRIGGER audit_users_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.log_audit();

DROP TRIGGER IF EXISTS audit_deposits_trigger ON public.deposits;
CREATE TRIGGER audit_deposits_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.deposits
    FOR EACH ROW EXECUTE FUNCTION public.log_audit();

DROP TRIGGER IF EXISTS audit_retribusi_categories_trigger ON public.retribusi_categories;
CREATE TRIGGER audit_retribusi_categories_trigger
    AFTER INSERT OR UPDATE OR DELETE ON public.retribusi_categories
    FOR EACH ROW EXECUTE FUNCTION public.log_audit();

-- =============================================
-- 9. Create Helper Functions
-- =============================================

-- Function to get user role
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT AS $$
BEGIN
    RETURN (SELECT role FROM public.users WHERE id = user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user can access category
CREATE OR REPLACE FUNCTION public.can_user_access_category(user_id UUID, category_id UUID, action TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    user_role_val TEXT;
    can_access BOOLEAN := false;
BEGIN
    -- Get user role
    SELECT role INTO user_role_val FROM public.users WHERE id = user_id;
    
    -- Admin can access everything
    IF user_role_val = 'ADMIN' THEN
        RETURN true;
    END IF;
    
    -- Check specific permissions
    SELECT 
        CASE 
            WHEN action = 'create' THEN can_create
            WHEN action = 'edit' THEN can_edit
            WHEN action = 'delete' THEN can_delete
            ELSE false
        END INTO can_access
    FROM public.role_retribusi_assignments 
    WHERE user_role = user_role_val AND retribusi_category_id = category_id;
    
    RETURN COALESCE(can_access, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
