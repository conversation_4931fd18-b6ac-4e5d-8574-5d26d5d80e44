export const mockTransactions = [
  {
    id: 'TR-20250601-001',
    name: '<PERSON><PERSON>',
    category: 'Parkir',
    amount: 250000,
    date: '01 Juni 2025',
    time: '09:30',
    description: 'Setoran retribusi parkir area Taman Kota periode 31 Mei 2025.',
    paymentMethod: 'Tunai',
    receiptImage: 'https://images.pexels.com/photos/3943723/pexels-photo-3943723.jpeg'
  },
  {
    id: 'TR-********-005',
    name: '<PERSON><PERSON>',
    category: 'Pasar',
    amount: 450000,
    date: '31 Mei 2025',
    time: '14:15',
    description: 'Setoran retribusi pasar untuk 10 kios di Pasar Minggu periode minggu keempat Mei 2025.',
    paymentMethod: 'Transfer Bank',
    referenceNumber: 'BCA-********',
    receiptImage: 'https://images.pexels.com/photos/4386366/pexels-photo-4386366.jpeg'
  },
  {
    id: 'TR-********-003',
    name: '<PERSON>',
    category: 'Terminal',
    amount: 350000,
    date: '31 Mei 2025',
    time: '11:45',
    description: 'Setoran retribusi terminal bus untuk 5 bus dengan trayek kota.',
    paymentMethod: 'QRIS',
    referenceNumber: 'QRIS-********'
  },
  {
    id: 'TR-********-009',
    name: 'Dewi Lestari',
    category: 'Parkir',
    amount: 180000,
    date: '30 Mei 2025',
    time: '16:20',
    description: 'Setoran retribusi parkir mall periode pagi.',
    paymentMethod: 'Tunai'
  },
  {
    id: 'TR-********-007',
    name: 'Joko Widodo',
    category: 'Pasar',
    amount: 275000,
    date: '30 Mei 2025',
    time: '13:10',
    description: 'Setoran retribusi pasar untuk 6 kios di Pasar Baru.',
    paymentMethod: 'Tunai'
  }
];

export const mockDashboardData = {
  currentDate: '01 Juni 2025',
  todayTotal: 250000,
  todayTransactions: 1,
  percentageChange: 11.2, // positive percentage change compared to yesterday
  recentTransactions: mockTransactions.slice(0, 3), // Use first 3 transactions as recent
  monthlyTotal: 8750000,
  monthlyPercentageChange: 5.3,
  targetPercentage: 72 // Percentage of monthly target achieved
};

export const mockReportData = {
  dateRange: '1 - 31 Mei 2025',
  totalAmount: 12500000,
  totalTransactions: 48,
  averageAmount: 260416.67,
  categoryDistribution: [
    { name: 'Parkir', percentage: 42 },
    { name: 'Pasar', percentage: 35 },
    { name: 'Terminal', percentage: 23 }
  ]
};