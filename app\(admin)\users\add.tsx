import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft, User, Mail, Lock, Building, Briefcase, Phone } from 'lucide-react-native';

import StatusBar from '@/components/StatusBar';

export default function AddUserScreen() {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    full_name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'USER',
    department: '',
    position: '',
    phone: '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  const roles = [
    { value: 'USER', label: 'User Biasa', description: 'Dapat membuat dan melihat setoran sendiri' },
    { value: 'SUPERVISOR', label: 'Supervisor', description: 'Dapat approve/reject setoran' },
    { value: 'ADMIN', label: 'Administrator', description: 'Aks<PERSON> penuh ke semua fitur' },
  ];

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Nama lengkap wajib diisi';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email wajib diisi';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Format email tidak valid';
    }

    if (!formData.password.trim()) {
      newErrors.password = 'Password wajib diisi';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password minimal 6 karakter';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Konfirmasi password tidak cocok';
    }

    if (!formData.department.trim()) {
      newErrors.department = 'Departemen wajib diisi';
    }

    if (!formData.position.trim()) {
      newErrors.position = 'Jabatan wajib diisi';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      setLoading(true);
      
      // Mock user creation
      console.log('Creating user:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      Alert.alert(
        'Berhasil!',
        `User ${formData.full_name} berhasil didaftarkan dengan role ${formData.role}.`,
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          }
        ]
      );
    } catch (error) {
      console.error('Error creating user:', error);
      Alert.alert('Error', 'Gagal mendaftarkan user. Silakan coba lagi.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    Alert.alert(
      'Batalkan?',
      'Data yang sudah diisi akan hilang. Yakin ingin membatalkan?',
      [
        { text: 'Tidak', style: 'cancel' },
        { text: 'Ya, Batalkan', style: 'destructive', onPress: () => router.back() },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <ArrowLeft size={24} color="#0A2463" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tambah User Baru</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Personal Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Personal</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Nama Lengkap <Text style={styles.required}>*</Text></Text>
            <View style={[styles.inputContainer, errors.full_name && styles.inputError]}>
              <User size={20} color="#6B7280" />
              <TextInput
                style={styles.input}
                placeholder="Masukkan nama lengkap"
                value={formData.full_name}
                onChangeText={(value) => {
                  setFormData(prev => ({ ...prev, full_name: value }));
                  if (errors.full_name) setErrors(prev => ({ ...prev, full_name: '' }));
                }}
              />
            </View>
            {errors.full_name && <Text style={styles.errorText}>{errors.full_name}</Text>}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email <Text style={styles.required}>*</Text></Text>
            <View style={[styles.inputContainer, errors.email && styles.inputError]}>
              <Mail size={20} color="#6B7280" />
              <TextInput
                style={styles.input}
                placeholder="<EMAIL>"
                value={formData.email}
                onChangeText={(value) => {
                  setFormData(prev => ({ ...prev, email: value }));
                  if (errors.email) setErrors(prev => ({ ...prev, email: '' }));
                }}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>
            {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>No. Telepon</Text>
            <View style={styles.inputContainer}>
              <Phone size={20} color="#6B7280" />
              <TextInput
                style={styles.input}
                placeholder="+*************"
                value={formData.phone}
                onChangeText={(value) => setFormData(prev => ({ ...prev, phone: value }))}
                keyboardType="phone-pad"
              />
            </View>
          </View>
        </View>

        {/* Account Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Akun</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Password <Text style={styles.required}>*</Text></Text>
            <View style={[styles.inputContainer, errors.password && styles.inputError]}>
              <Lock size={20} color="#6B7280" />
              <TextInput
                style={styles.input}
                placeholder="Minimal 6 karakter"
                value={formData.password}
                onChangeText={(value) => {
                  setFormData(prev => ({ ...prev, password: value }));
                  if (errors.password) setErrors(prev => ({ ...prev, password: '' }));
                }}
                secureTextEntry
              />
            </View>
            {errors.password && <Text style={styles.errorText}>{errors.password}</Text>}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Konfirmasi Password <Text style={styles.required}>*</Text></Text>
            <View style={[styles.inputContainer, errors.confirmPassword && styles.inputError]}>
              <Lock size={20} color="#6B7280" />
              <TextInput
                style={styles.input}
                placeholder="Ulangi password"
                value={formData.confirmPassword}
                onChangeText={(value) => {
                  setFormData(prev => ({ ...prev, confirmPassword: value }));
                  if (errors.confirmPassword) setErrors(prev => ({ ...prev, confirmPassword: '' }));
                }}
                secureTextEntry
              />
            </View>
            {errors.confirmPassword && <Text style={styles.errorText}>{errors.confirmPassword}</Text>}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Role <Text style={styles.required}>*</Text></Text>
            <View style={styles.roleContainer}>
              {roles.map((role) => (
                <TouchableOpacity
                  key={role.value}
                  style={[
                    styles.roleOption,
                    formData.role === role.value && styles.roleOptionSelected
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, role: role.value }))}
                >
                  <Text style={[
                    styles.roleLabel,
                    formData.role === role.value && styles.roleLabelSelected
                  ]}>
                    {role.label}
                  </Text>
                  <Text style={styles.roleDescription}>{role.description}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Work Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Informasi Pekerjaan</Text>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Departemen <Text style={styles.required}>*</Text></Text>
            <View style={[styles.inputContainer, errors.department && styles.inputError]}>
              <Building size={20} color="#6B7280" />
              <TextInput
                style={styles.input}
                placeholder="Contoh: Dinas Pendapatan Daerah"
                value={formData.department}
                onChangeText={(value) => {
                  setFormData(prev => ({ ...prev, department: value }));
                  if (errors.department) setErrors(prev => ({ ...prev, department: '' }));
                }}
              />
            </View>
            {errors.department && <Text style={styles.errorText}>{errors.department}</Text>}
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Jabatan <Text style={styles.required}>*</Text></Text>
            <View style={[styles.inputContainer, errors.position && styles.inputError]}>
              <Briefcase size={20} color="#6B7280" />
              <TextInput
                style={styles.input}
                placeholder="Contoh: Petugas Retribusi"
                value={formData.position}
                onChangeText={(value) => {
                  setFormData(prev => ({ ...prev, position: value }));
                  if (errors.position) setErrors(prev => ({ ...prev, position: '' }));
                }}
              />
            </View>
            {errors.position && <Text style={styles.errorText}>{errors.position}</Text>}
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleCancel} disabled={loading}>
            <Text style={styles.cancelButtonText}>Batal</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.submitButton, loading && styles.submitButtonDisabled]} 
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.submitButtonText}>
              {loading ? 'Menyimpan...' : 'Daftarkan User'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  placeholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: '#FFFFFF',
    margin: 16,
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 8,
  },
  required: {
    color: '#EF4444',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    gap: 12,
  },
  inputError: {
    borderColor: '#EF4444',
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#1F2937',
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  roleContainer: {
    gap: 8,
  },
  roleOption: {
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    backgroundColor: '#F9FAFB',
  },
  roleOptionSelected: {
    borderColor: '#0A2463',
    backgroundColor: '#EEF2FF',
  },
  roleLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  roleLabelSelected: {
    color: '#0A2463',
  },
  roleDescription: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    padding: 16,
    paddingBottom: 32,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#FFFFFF',
    alignItems: 'center',
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
  },
  submitButton: {
    flex: 2,
    paddingVertical: 14,
    borderRadius: 8,
    backgroundColor: '#0A2463',
    alignItems: 'center',
  },
  submitButtonDisabled: {
    opacity: 0.6,
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});
