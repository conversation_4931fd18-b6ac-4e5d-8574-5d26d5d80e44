import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';
import { CircleArrowRight as ArrowRightCircle, TrendingUp, TrendingDown, DollarSign, Layers, Clock, RefreshCcw, Plus, LogOut } from 'lucide-react-native';

import StatusBar from '@/components/StatusBar';
import { mockDashboardData } from '@/data/mockData';
import { useAuth } from '@/contexts/AuthContext';

export default function HomeScreen() {
  const { profile, signOut } = useAuth();
  const [dashboardData, setDashboardData] = useState(mockDashboardData);
  const [loading, setLoading] = useState(false);

  const refreshData = () => {
    setLoading(true);

    // Simulate data refresh with a timeout
    setTimeout(() => {
      setLoading(false);
    }, 1500);
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      '<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin keluar dari aplikasi?',
      [
        {
          text: 'Bat<PERSON>',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              console.log('Logging out from dashboard...');
              await signOut();
              router.replace('/auth/login');
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Error', 'Gagal logout. Silakan coba lagi.');
            }
          },
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar />
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <View>
            <Text style={styles.greeting}>Selamat Datang,</Text>
            <Text style={styles.userName}>{profile?.full_name || 'User'}</Text>
          </View>
          <View style={styles.headerActions}>
            <TouchableOpacity style={styles.refreshButton} onPress={refreshData}>
              <RefreshCcw size={20} color="#0A2463" style={{ opacity: loading ? 0.5 : 1 }} />
            </TouchableOpacity>
            <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
              <LogOut size={18} color="#EF4444" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.summaryContainer}>
          <View style={styles.summaryHeader}>
            <Text style={styles.summaryTitle}>Ringkasan Hari Ini</Text>
            <Text style={styles.summaryDate}>{dashboardData.currentDate}</Text>
          </View>

          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <View style={styles.statIconContainer}>
                <DollarSign size={20} color="#206A5D" />
              </View>
              <Text style={styles.statValue}>Rp {dashboardData.todayTotal.toLocaleString()}</Text>
              <Text style={styles.statLabel}>Total Hari Ini</Text>
            </View>

            <View style={styles.statCard}>
              <View style={styles.statIconContainer}>
                <Layers size={20} color="#206A5D" />
              </View>
              <Text style={styles.statValue}>{dashboardData.todayTransactions}</Text>
              <Text style={styles.statLabel}>Jumlah Transaksi</Text>
            </View>
          </View>

          <View style={styles.comparisonContainer}>
            <View style={[styles.comparisonItem, dashboardData.percentageChange >= 0 ? styles.positive : styles.negative]}>
              {dashboardData.percentageChange >= 0 ? (
                <TrendingUp size={16} color="#047857" />
              ) : (
                <TrendingDown size={16} color="#B91C1C" />
              )}
              <Text style={[styles.comparisonText, dashboardData.percentageChange >= 0 ? styles.positiveText : styles.negativeText]}>
                {Math.abs(dashboardData.percentageChange)}% dari kemarin
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.recentTransactionsContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Setoran Terbaru</Text>
            <TouchableOpacity onPress={() => router.push('/(tabs)/deposits')}>
              <Text style={styles.seeAllText}>Lihat Semua</Text>
            </TouchableOpacity>
          </View>

          {dashboardData.recentTransactions.map((transaction, index) => (
            <TouchableOpacity 
              key={transaction.id}
              style={[
                styles.transactionCard, 
                index === dashboardData.recentTransactions.length - 1 && styles.lastCard
              ]}
              onPress={() => router.push({
                pathname: '/(tabs)/deposits/[id]',
                params: { id: transaction.id }
              })}
            >
              <View style={styles.transactionLeft}>
                <View style={[styles.categoryTag, { backgroundColor: getCategoryColor(transaction.category) }]}>
                  <Text style={styles.categoryText}>{transaction.category}</Text>
                </View>
                <Text style={styles.transactionName}>{transaction.name}</Text>
                <View style={styles.transactionMeta}>
                  <Clock size={12} color="#6B7280" />
                  <Text style={styles.transactionTime}>{transaction.time}</Text>
                </View>
              </View>
              <View style={styles.transactionRight}>
                <Text style={styles.transactionAmount}>Rp {transaction.amount.toLocaleString()}</Text>
                <ArrowRightCircle size={16} color="#0A2463" />
              </View>
            </TouchableOpacity>
          ))}
        </View>

        <View style={styles.summaryStats}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Ringkasan Bulanan</Text>
            <TouchableOpacity onPress={() => router.push('/(tabs)/reports')}>
              <Text style={styles.seeAllText}>Detail</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.monthlyStatsContainer}>
            <View style={styles.monthlyStatCard}>
              <Text style={styles.monthlyStatLabel}>Total Bulan Ini</Text>
              <Text style={styles.monthlyStatValue}>Rp {dashboardData.monthlyTotal.toLocaleString()}</Text>
              <View style={styles.monthlyStatChange}>
                {dashboardData.monthlyPercentageChange >= 0 ? (
                  <TrendingUp size={14} color="#047857" />
                ) : (
                  <TrendingDown size={14} color="#B91C1C" />
                )}
                <Text 
                  style={[
                    styles.monthlyStatChangeText,
                    dashboardData.monthlyPercentageChange >= 0 ? styles.positiveText : styles.negativeText
                  ]}
                >
                  {Math.abs(dashboardData.monthlyPercentageChange)}%
                </Text>
              </View>
            </View>

            <View style={styles.monthlyStatCard}>
              <Text style={styles.monthlyStatLabel}>Target Pencapaian</Text>
              <Text style={styles.monthlyStatValue}>{dashboardData.targetPercentage}%</Text>
              <View style={styles.progressBar}>
                <View 
                  style={[
                    styles.progressFill,
                    { width: `${dashboardData.targetPercentage}%` }
                  ]} 
                />
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      <TouchableOpacity 
        style={styles.fab}
        onPress={() => router.push('/(tabs)/deposits/add')}
      >
        <Plus size={24} color="#FFFFFF" />
      </TouchableOpacity>
    </View>
  );
}

// Helper function to get category color
const getCategoryColor = (category: string) => {
  const colorMap: Record<string, string> = {
    'Parkir': '#F59E0B',
    'Pasar': '#10B981',
    'Terminal': '#6366F1',
    'default': '#0A2463'
  };
  
  return colorMap[category] || colorMap.default;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 24,
    backgroundColor: '#FFFFFF',
  },
  greeting: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    color: '#6B7280',
  },
  userName: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 20,
    color: '#111827',
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  logoutButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#FEF2F2',
  },
  summaryContainer: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: -16,
    borderRadius: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    marginBottom: 16,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  summaryTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#111827',
  },
  summaryDate: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    color: '#6B7280',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginRight: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginRight: 8,
  },
  statIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(32, 106, 93, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  statValue: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#111827',
    marginBottom: 4,
  },
  statLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    color: '#6B7280',
  },
  comparisonContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  comparisonItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
  },
  positive: {
    backgroundColor: 'rgba(4, 120, 87, 0.1)',
  },
  negative: {
    backgroundColor: 'rgba(185, 28, 28, 0.1)',
  },
  comparisonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 12,
    marginLeft: 4,
  },
  positiveText: {
    color: '#047857',
  },
  negativeText: {
    color: '#B91C1C',
  },
  recentTransactionsContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#111827',
  },
  seeAllText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#0A2463',
  },
  transactionCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  lastCard: {
    borderBottomWidth: 0,
  },
  transactionLeft: {
    flex: 1,
  },
  categoryTag: {
    alignSelf: 'flex-start',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
    marginBottom: 6,
  },
  categoryText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 10,
    color: '#FFFFFF',
  },
  transactionName: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#111827',
    marginBottom: 4,
  },
  transactionMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionTime: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    color: '#6B7280',
    marginLeft: 4,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 14,
    color: '#111827',
    marginBottom: 4,
  },
  summaryStats: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginHorizontal: 16,
    marginBottom: 24,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  monthlyStatsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  monthlyStatCard: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    marginRight: 8,
  },
  monthlyStatLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 8,
  },
  monthlyStatValue: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#111827',
    marginBottom: 8,
  },
  monthlyStatChange: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  monthlyStatChangeText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 12,
    marginLeft: 4,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#0A2463',
    borderRadius: 4,
  },
  fab: {
    position: 'absolute',
    bottom: 24,
    right: 24,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#0A2463',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5,
  },
});