import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Image, Share } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { ArrowLeft, Download, Share2, Circle<PERSON>heck as CheckCircle2, Trash2 } from 'lucide-react-native';

import StatusBar from '@/components/StatusBar';
import { mockTransactions } from '@/data/mockData';

export default function DepositDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const [transaction, setTransaction] = useState<(typeof mockTransactions)[0] | null>(null);

  useEffect(() => {
    // In a real app, this would be an API call to fetch the transaction details
    const foundTransaction = mockTransactions.find(t => t.id === id);
    setTransaction(foundTransaction || null);
  }, [id]);

  const handleShare = async () => {
    if (!transaction) return;
    
    try {
      await Share.share({
        message: `Setoran Retribusi\nID: ${transaction.id}\nNama: ${transaction.name}\nJumlah: Rp ${transaction.amount.toLocaleString()}\nTanggal: ${transaction.date}`,
        title: 'Detail Setoran Retribusi',
      });
    } catch (error) {
      console.error('Error sharing:', error);
    }
  };

  const handleDelete = () => {
    // In a real app, this would be an API call to delete the transaction
    // For demo purposes, we'll just navigate back to the deposits list
    router.replace('/(tabs)/deposits');
  };

  if (!transaction) {
    return (
      <View style={styles.container}>
        <StatusBar />
        <View style={styles.header}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color="#111827" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Detail Setoran</Text>
        </View>
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>Data setoran tidak ditemukan</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detail Setoran</Text>
        <TouchableOpacity style={styles.shareButton} onPress={handleShare}>
          <Share2 size={20} color="#0A2463" />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView}>
        <View style={styles.statusCard}>
          <CheckCircle2 size={24} color="#16A34A" />
          <Text style={styles.statusText}>Setoran Terverifikasi</Text>
        </View>

        <View style={styles.detailCard}>
          <View style={styles.idContainer}>
            <Text style={styles.idLabel}>ID Setoran</Text>
            <Text style={styles.idValue}>{transaction.id}</Text>
          </View>

          <View style={styles.amountContainer}>
            <Text style={styles.amountLabel}>Jumlah</Text>
            <Text style={styles.amountValue}>Rp {transaction.amount.toLocaleString()}</Text>
          </View>

          <View style={styles.divider} />

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Nama Penyetor</Text>
            <Text style={styles.detailValue}>{transaction.name}</Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Kategori</Text>
            <View style={[styles.categoryTag, { backgroundColor: getCategoryColor(transaction.category) }]}>
              <Text style={styles.categoryText}>{transaction.category}</Text>
            </View>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Tanggal</Text>
            <Text style={styles.detailValue}>{transaction.date}</Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Waktu</Text>
            <Text style={styles.detailValue}>{transaction.time}</Text>
          </View>

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Metode Pembayaran</Text>
            <Text style={styles.detailValue}>{transaction.paymentMethod || 'Tunai'}</Text>
          </View>

          {transaction.referenceNumber && (
            <View style={styles.detailItem}>
              <Text style={styles.detailLabel}>Nomor Referensi</Text>
              <Text style={styles.detailValue}>{transaction.referenceNumber}</Text>
            </View>
          )}

          <View style={styles.detailItem}>
            <Text style={styles.detailLabel}>Petugas</Text>
            <Text style={styles.detailValue}>{transaction.officer || 'Ahmad Fauzi'}</Text>
          </View>

          {transaction.description && (
            <View style={styles.descriptionContainer}>
              <Text style={styles.descriptionLabel}>Deskripsi</Text>
              <Text style={styles.descriptionValue}>{transaction.description}</Text>
            </View>
          )}
        </View>

        {transaction.receiptImage && (
          <View style={styles.receiptCard}>
            <Text style={styles.receiptTitle}>Bukti Pembayaran</Text>
            <Image 
              source={{ uri: transaction.receiptImage }} 
              style={styles.receiptImage}
              resizeMode="cover"
            />
            <TouchableOpacity style={styles.downloadButton}>
              <Download size={16} color="#0A2463" />
              <Text style={styles.downloadText}>Unduh Bukti Pembayaran</Text>
            </TouchableOpacity>
          </View>
        )}

        <TouchableOpacity style={styles.deleteButton} onPress={handleDelete}>
          <Trash2 size={20} color="#FFFFFF" />
          <Text style={styles.deleteButtonText}>Hapus Setoran</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}

// Helper function to get category color
const getCategoryColor = (category: string) => {
  const colorMap: Record<string, string> = {
    'Parkir': '#F59E0B',
    'Pasar': '#10B981',
    'Terminal': '#6366F1',
    'default': '#0A2463'
  };
  
  return colorMap[category] || colorMap.default;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 18,
    color: '#111827',
    marginLeft: 8,
    flex: 1,
  },
  shareButton: {
    padding: 8,
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
  },
  statusCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ECFDF5',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  statusText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    color: '#16A34A',
    marginLeft: 12,
  },
  detailCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  idContainer: {
    marginBottom: 8,
  },
  idLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    color: '#6B7280',
  },
  idValue: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#111827',
  },
  amountContainer: {
    marginBottom: 16,
  },
  amountLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    color: '#6B7280',
  },
  amountValue: {
    fontFamily: 'Poppins-Bold',
    fontSize: 24,
    color: '#111827',
  },
  divider: {
    height: 1,
    backgroundColor: '#E5E7EB',
    marginVertical: 16,
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  detailLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    color: '#6B7280',
  },
  detailValue: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#111827',
  },
  categoryTag: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4,
  },
  categoryText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 12,
    color: '#FFFFFF',
  },
  descriptionContainer: {
    marginTop: 8,
  },
  descriptionLabel: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#374151',
    marginBottom: 4,
  },
  descriptionValue: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  receiptCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  receiptTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#111827',
    marginBottom: 12,
  },
  receiptImage: {
    width: '100%',
    height: 200,
    borderRadius: 8,
    marginBottom: 12,
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EBF5FF',
    borderWidth: 1,
    borderColor: '#BFDBFE',
    borderRadius: 8,
    paddingVertical: 10,
  },
  downloadText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#0A2463',
    marginLeft: 8,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EF4444',
    borderRadius: 8,
    paddingVertical: 14,
    marginBottom: 24,
  },
  deleteButtonText: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#FFFFFF',
    marginLeft: 8,
  },
});