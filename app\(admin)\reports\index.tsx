import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { FileText, TrendingUp, Users, DollarSign } from 'lucide-react-native';

import StatusBar from '@/components/StatusBar';

export default function AdminReportsScreen() {
  return (
    <View style={styles.container}>
      <StatusBar />
      
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.headerTitle}>Laporan Admin</Text>
          <Text style={styles.headerSubtitle}>Lihat semua laporan dan setoran</Text>
        </View>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.content}>
          <View style={styles.comingSoonCard}>
            <FileText size={48} color="#D1D5DB" />
            <Text style={styles.comingSoonTitle}>Fitur Laporan Admin</Text>
            <Text style={styles.comingSoonDescription}>
              Fitur ini akan menampilkan semua laporan setoran dari semua user, 
              dengan kemampuan approve/reject dan export data.
            </Text>
            <Text style={styles.comingSoonNote}>
              Coming soon in next update!
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
    paddingTop: 60,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
  },
  headerSubtitle: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  comingSoonCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  comingSoonTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#374151',
    marginTop: 16,
    textAlign: 'center',
  },
  comingSoonDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 8,
    lineHeight: 20,
  },
  comingSoonNote: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 16,
    fontStyle: 'italic',
  },
});
