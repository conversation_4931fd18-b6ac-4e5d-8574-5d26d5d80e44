import { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Alert } from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft, Camera } from 'lucide-react-native';
import { Picker } from '@react-native-picker/picker';

import StatusBar from '@/components/StatusBar';

export default function AddDepositScreen() {
  const [formData, setFormData] = useState({
    name: '',
    category: 'Parkir',
    amount: '',
    description: '',
    paymentMethod: 'Tunai',
    referenceNumber: '',
  });

  const categories = ['Parkir', 'Pasar', 'Terminal'];
  const paymentMethods = ['Tunai', 'Transfer Bank', 'QRIS'];

  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  const handleSubmit = () => {
    // Validate form
    if (!formData.name.trim()) {
      Alert.alert('Error', 'Nama wajib diisi');
      return;
    }

    if (!formData.amount.trim()) {
      Alert.alert('Error', 'Jumlah wajib diisi');
      return;
    }

    // In a real app, this would be an API call to save the deposit
    // For demo purposes, we'll just navigate back to the deposits list
    Alert.alert(
      'Sukses',
      'Setoran berhasil ditambahkan',
      [
        {
          text: 'OK',
          onPress: () => router.replace('/(tabs)/deposits')
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tambah Setoran</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <View style={styles.formContainer}>
          <View style={styles.formGroup}>
            <Text style={styles.label}>Nama Penyetor</Text>
            <TextInput
              style={styles.input}
              placeholder="Masukkan nama penyetor"
              value={formData.name}
              onChangeText={(value) => handleInputChange('name', value)}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Kategori</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={formData.category}
                onValueChange={(value) => handleInputChange('category', value)}
                style={styles.picker}
              >
                {categories.map((category) => (
                  <Picker.Item key={category} label={category} value={category} />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Jumlah (Rp)</Text>
            <TextInput
              style={styles.input}
              placeholder="0"
              value={formData.amount}
              onChangeText={(value) => handleInputChange('amount', value)}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Deskripsi (Opsional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Tambahkan deskripsi atau catatan"
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Metode Pembayaran</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={formData.paymentMethod}
                onValueChange={(value) => handleInputChange('paymentMethod', value)}
                style={styles.picker}
              >
                {paymentMethods.map((method) => (
                  <Picker.Item key={method} label={method} value={method} />
                ))}
              </Picker>
            </View>
          </View>

          {formData.paymentMethod !== 'Tunai' && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Nomor Referensi</Text>
              <TextInput
                style={styles.input}
                placeholder="Masukkan nomor referensi"
                value={formData.referenceNumber}
                onChangeText={(value) => handleInputChange('referenceNumber', value)}
              />
            </View>
          )}

          <TouchableOpacity style={styles.uploadButton}>
            <Camera size={20} color="#0A2463" />
            <Text style={styles.uploadButtonText}>Unggah Bukti Pembayaran</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <Text style={styles.submitButtonText}>Simpan Setoran</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 18,
    color: '#111827',
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#374151',
    marginBottom: 6,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    color: '#1F2937',
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
  },
  pickerContainer: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
  },
  picker: {
    height: 50,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EBF5FF',
    borderWidth: 1,
    borderColor: '#BFDBFE',
    borderRadius: 8,
    paddingVertical: 14,
    marginBottom: 24,
  },
  uploadButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#0A2463',
    marginLeft: 8,
  },
  submitButton: {
    backgroundColor: '#0A2463',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 24,
  },
  submitButtonText: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#FFFFFF',
  },
});