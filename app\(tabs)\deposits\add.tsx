import React, { useState } from 'react';
import { View, Text, StyleSheet, Alert } from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';

import StatusBar from '@/components/StatusBar';
import DepositForm from '@/components/forms/DepositForm';
import { useAuth } from '@/contexts/AuthContext';
import { DepositsService } from '@/services/depositsService';
import type { DepositInsert } from '@/lib/supabase';

export default function AddDepositScreen() {
  const { profile } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (depositData: DepositInsert) => {
    if (!profile) {
      Alert.alert('Error', 'User tidak ditemukan');
      return;
    }

    try {
      setLoading(true);

      // Create deposit
      const newDeposit = await DepositsService.createDeposit(depositData);

      Alert.alert(
        'Berhasil!',
        'Setoran berhasil disimpan dan menunggu persetujuan.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate back to deposits list
              router.back();
            }
          }
        ]
      );
    } catch (error) {
      console.error('Error creating deposit:', error);
      Alert.alert(
        'Error',
        'Gagal menyimpan setoran. Silakan coba lagi.'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    Alert.alert(
      'Batalkan?',
      'Data yang sudah diisi akan hilang. Yakin ingin membatalkan?',
      [
        {
          text: 'Tidak',
          style: 'cancel',
        },
        {
          text: 'Ya, Batalkan',
          style: 'destructive',
          onPress: () => router.back(),
        },
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#0A2463" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tambah Setoran</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Form */}
      <DepositForm
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        loading={loading}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  placeholder: {
    width: 40,
  },
});