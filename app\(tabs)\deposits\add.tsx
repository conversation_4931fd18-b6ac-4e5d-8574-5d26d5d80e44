import { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, Alert, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft, Camera, Building2 } from 'lucide-react-native';
import { Picker } from '@react-native-picker/picker';

import StatusBar from '@/components/StatusBar';
import { useAuth } from '../../../contexts/AuthContext';
import { opdService, type OPD } from '../../../services/opdService';
import { supabase } from '../../../lib/supabase';

export default function AddDepositScreen() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [userOPDs, setUserOPDs] = useState<OPD[]>([]);
  const [selectedOPD, setSelectedOPD] = useState<string>('');
  const [retribusiCategories, setRetribusiCategories] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    amount: '',
    description: '',
    paymentMethod: 'Tunai',
    referenceNumber: '',
  });

  const paymentMethods = ['Tunai', 'Transfer Bank', 'QRIS'];

  useEffect(() => {
    if (!user) {
      Alert.alert('Error', 'User tidak ditemukan');
      router.back();
      return;
    }
    loadUserOPDs();
  }, [user]);

  useEffect(() => {
    if (selectedOPD) {
      loadRetribusiCategories();
    }
  }, [selectedOPD]);

  const loadUserOPDs = async () => {
    try {
      setLoading(true);
      const opds = await opdService.getUserOPDs(user!.id);
      setUserOPDs(opds);

      // Auto-select OPD if user only has one
      if (opds.length === 1) {
        setSelectedOPD(opds[0].id);
      }
    } catch (error) {
      console.error('Error loading user OPDs:', error);
      Alert.alert('Error', 'Gagal memuat data OPD');
    } finally {
      setLoading(false);
    }
  };

  const loadRetribusiCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('retribusi_categories')
        .select(`
          id,
          nomor_rekening,
          nama_retribusi,
          base_tariff,
          jenis_retribusi:jenis_retribusi_id (
            name,
            code
          )
        `)
        .eq('opd_id', selectedOPD)
        .eq('is_active', true)
        .order('nomor_rekening');

      if (error) throw error;

      setRetribusiCategories(data || []);

      // Reset category selection when OPD changes
      setFormData(prev => ({ ...prev, category: '' }));
    } catch (error) {
      console.error('Error loading retribusi categories:', error);
      Alert.alert('Error', 'Gagal memuat kategori retribusi');
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  const handleOPDChange = (opdId: string) => {
    setSelectedOPD(opdId);
  };

  const handleSubmit = () => {
    // Validate form
    if (!formData.name.trim()) {
      Alert.alert('Error', 'Nama wajib diisi');
      return;
    }

    if (!selectedOPD) {
      Alert.alert('Error', 'OPD harus dipilih');
      return;
    }

    if (!formData.category) {
      Alert.alert('Error', 'Kategori retribusi harus dipilih');
      return;
    }

    if (!formData.amount.trim()) {
      Alert.alert('Error', 'Jumlah wajib diisi');
      return;
    }

    // In a real app, this would be an API call to save the deposit
    // For demo purposes, we'll just navigate back to the deposits list
    Alert.alert(
      'Sukses',
      'Setoran berhasil ditambahkan',
      [
        {
          text: 'OK',
          onPress: () => router.replace('/(tabs)/deposits')
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar />
      
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#111827" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Tambah Setoran</Text>
      </View>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text style={styles.loadingText}>Memuat data...</Text>
          </View>
        ) : (
          <View style={styles.formContainer}>
            <View style={styles.formGroup}>
              <Text style={styles.label}>Nama Penyetor</Text>
              <TextInput
                style={styles.input}
                placeholder="Masukkan nama penyetor"
                value={formData.name}
                onChangeText={(value) => handleInputChange('name', value)}
              />
            </View>

            {/* OPD Selection - only show if user has multiple OPDs */}
            {userOPDs.length > 1 && (
              <View style={styles.formGroup}>
                <Text style={styles.label}>Pilih OPD</Text>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={selectedOPD}
                    onValueChange={(value) => setSelectedOPD(value)}
                    style={styles.picker}
                  >
                    <Picker.Item label="Pilih OPD..." value="" />
                    {userOPDs.map((opd) => (
                      <Picker.Item key={opd.id} label={`${opd.name} (${opd.code})`} value={opd.id} />
                    ))}
                  </Picker>
                </View>
              </View>
            )}

            {/* Show selected OPD info if user has only one OPD */}
            {userOPDs.length === 1 && (
              <View style={styles.opdInfoContainer}>
                <Building2 size={20} color="#3B82F6" />
                <View style={styles.opdInfo}>
                  <Text style={styles.opdName}>{userOPDs[0].name}</Text>
                  <Text style={styles.opdCode}>{userOPDs[0].code}</Text>
                </View>
              </View>
            )}

            <View style={styles.formGroup}>
              <Text style={styles.label}>Kategori Retribusi</Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={formData.category}
                  onValueChange={(value) => handleInputChange('category', value)}
                  style={styles.picker}
                  enabled={!!selectedOPD}
                >
                  <Picker.Item label="Pilih kategori retribusi..." value="" />
                  {retribusiCategories.map((category) => (
                    <Picker.Item
                      key={category.id}
                      label={`${category.nomor_rekening} - ${category.nama_retribusi}`}
                      value={category.id}
                    />
                  ))}
                </Picker>
              </View>
              {!selectedOPD && (
                <Text style={styles.helperText}>Pilih OPD terlebih dahulu</Text>
              )}
            </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Jumlah (Rp)</Text>
            <TextInput
              style={styles.input}
              placeholder="0"
              value={formData.amount}
              onChangeText={(value) => handleInputChange('amount', value)}
              keyboardType="numeric"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Deskripsi (Opsional)</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              placeholder="Tambahkan deskripsi atau catatan"
              value={formData.description}
              onChangeText={(value) => handleInputChange('description', value)}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={styles.label}>Metode Pembayaran</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={formData.paymentMethod}
                onValueChange={(value) => handleInputChange('paymentMethod', value)}
                style={styles.picker}
              >
                {paymentMethods.map((method) => (
                  <Picker.Item key={method} label={method} value={method} />
                ))}
              </Picker>
            </View>
          </View>

          {formData.paymentMethod !== 'Tunai' && (
            <View style={styles.formGroup}>
              <Text style={styles.label}>Nomor Referensi</Text>
              <TextInput
                style={styles.input}
                placeholder="Masukkan nomor referensi"
                value={formData.referenceNumber}
                onChangeText={(value) => handleInputChange('referenceNumber', value)}
              />
            </View>
          )}

          <TouchableOpacity style={styles.uploadButton}>
            <Camera size={20} color="#0A2463" />
            <Text style={styles.uploadButtonText}>Unggah Bukti Pembayaran</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
            <Text style={styles.submitButtonText}>Simpan Setoran</Text>
          </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 18,
    color: '#111827',
    marginLeft: 8,
  },
  scrollView: {
    flex: 1,
  },
  formContainer: {
    padding: 16,
  },
  formGroup: {
    marginBottom: 16,
  },
  label: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#374151',
    marginBottom: 6,
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    color: '#1F2937',
  },
  textArea: {
    minHeight: 100,
    paddingTop: 12,
  },
  pickerContainer: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
  },
  picker: {
    height: 50,
  },
  uploadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#EBF5FF',
    borderWidth: 1,
    borderColor: '#BFDBFE',
    borderRadius: 8,
    paddingVertical: 14,
    marginBottom: 24,
  },
  uploadButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    color: '#0A2463',
    marginLeft: 8,
  },
  submitButton: {
    backgroundColor: '#0A2463',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 24,
  },
  submitButtonText: {
    fontFamily: 'Poppins-SemiBold',
    fontSize: 16,
    color: '#FFFFFF',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280',
  },
  opdInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EFF6FF',
    borderWidth: 1,
    borderColor: '#BFDBFE',
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
  },
  opdInfo: {
    marginLeft: 12,
  },
  opdName: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1E40AF',
    marginBottom: 2,
  },
  opdCode: {
    fontSize: 12,
    color: '#3B82F6',
  },
  helperText: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4,
    fontStyle: 'italic',
  },
});