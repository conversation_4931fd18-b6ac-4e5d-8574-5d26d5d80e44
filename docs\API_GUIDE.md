# API Guide - Report Retribusi

Dokumentasi lengkap untuk integrasi API dalam aplikasi Report Retribusi.

## 🌐 API Overview

API Report Retribusi menggunakan RESTful architecture dengan format JSON untuk pertukaran data. <PERSON><PERSON>a endpoint memerlukan authentication kecuali endpoint public.

### Base URL
```
Production: https://api.report-retribusi.com/v1
Development: http://localhost:3000/api/v1
```

### Authentication
```
Authorization: Bearer <access_token>
Content-Type: application/json
```

## 🔐 Authentication Endpoints

### 1. Login
```http
POST /auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "role": "user"
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIs...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
      "expires_in": 3600
    }
  }
}
```

### 2. Register
```http
POST /auth/register
```

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phone": "+6281234567890"
}
```

### 3. Refresh Token
```http
POST /auth/refresh
```

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIs..."
}
```

### 4. Logout
```http
POST /auth/logout
```

## 📊 Retribusi Endpoints

### 1. Get All Retribusi
```http
GET /retribusi
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status (pending, approved, rejected)
- `date_from` (optional): Filter from date (YYYY-MM-DD)
- `date_to` (optional): Filter to date (YYYY-MM-DD)

**Response:**
```json
{
  "success": true,
  "data": {
    "retribusi": [
      {
        "id": "ret_123",
        "title": "Retribusi Parkir",
        "amount": 50000,
        "status": "pending",
        "date": "2024-01-15",
        "location": "Jl. Sudirman No. 123",
        "description": "Pembayaran retribusi parkir bulanan",
        "attachments": [
          {
            "id": "att_123",
            "url": "https://storage.com/image.jpg",
            "type": "image"
          }
        ],
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 50,
      "per_page": 10
    }
  }
}
```

### 2. Get Retribusi by ID
```http
GET /retribusi/{id}
```

### 3. Create New Retribusi
```http
POST /retribusi
```

**Request Body:**
```json
{
  "title": "Retribusi Parkir",
  "amount": 50000,
  "location": "Jl. Sudirman No. 123",
  "description": "Pembayaran retribusi parkir bulanan",
  "category_id": "cat_123",
  "attachments": [
    {
      "file": "base64_encoded_image",
      "filename": "receipt.jpg",
      "type": "image/jpeg"
    }
  ]
}
```

### 4. Update Retribusi
```http
PUT /retribusi/{id}
```

### 5. Delete Retribusi
```http
DELETE /retribusi/{id}
```

## 📂 Categories Endpoints

### 1. Get All Categories
```http
GET /categories
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "cat_123",
      "name": "Parkir",
      "description": "Retribusi parkir kendaraan",
      "icon": "parking",
      "color": "#007AFF"
    },
    {
      "id": "cat_124",
      "name": "Kebersihan",
      "description": "Retribusi kebersihan lingkungan",
      "icon": "trash",
      "color": "#34C759"
    }
  ]
}
```

## 📸 File Upload Endpoints

### 1. Upload File
```http
POST /upload
```

**Request (multipart/form-data):**
```
file: [binary file]
type: image|document
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "file_123",
    "url": "https://storage.com/uploads/file_123.jpg",
    "filename": "receipt.jpg",
    "size": 1024000,
    "type": "image/jpeg"
  }
}
```

## 👤 User Profile Endpoints

### 1. Get Profile
```http
GET /profile
```

### 2. Update Profile
```http
PUT /profile
```

**Request Body:**
```json
{
  "name": "John Doe Updated",
  "phone": "+6281234567890",
  "address": "Jl. Sudirman No. 123, Jakarta"
}
```

## 📊 Dashboard Endpoints

### 1. Get Dashboard Stats
```http
GET /dashboard/stats
```

**Response:**
```json
{
  "success": true,
  "data": {
    "total_retribusi": 150,
    "pending_retribusi": 25,
    "approved_retribusi": 100,
    "rejected_retribusi": 25,
    "total_amount": 7500000,
    "monthly_stats": [
      {
        "month": "2024-01",
        "count": 45,
        "amount": 2250000
      }
    ]
  }
}
```

## 🔔 Notifications Endpoints

### 1. Get Notifications
```http
GET /notifications
```

### 2. Mark as Read
```http
PUT /notifications/{id}/read
```

## 🛠️ API Client Implementation

### 1. Base API Service

```typescript
// services/api.ts
class ApiService {
  private baseURL = 'https://api.report-retribusi.com/v1';
  private token: string | null = null;

  setToken(token: string) {
    this.token = token;
  }

  private async request(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      ...(this.token && { Authorization: `Bearer ${this.token}` }),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }

  async get(endpoint: string) {
    return this.request(endpoint);
  }

  async post(endpoint: string, data: any) {
    return this.request(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put(endpoint: string, data: any) {
    return this.request(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete(endpoint: string) {
    return this.request(endpoint, {
      method: 'DELETE',
    });
  }
}

export const apiService = new ApiService();
```

### 2. Auth Service

```typescript
// services/auth.ts
import { apiService } from './api';

export class AuthService {
  static async login(email: string, password: string) {
    const response = await apiService.post('/auth/login', {
      email,
      password,
    });
    
    if (response.success) {
      apiService.setToken(response.data.tokens.access_token);
    }
    
    return response;
  }

  static async register(userData: RegisterData) {
    return apiService.post('/auth/register', userData);
  }

  static async logout() {
    return apiService.post('/auth/logout', {});
  }
}
```

### 3. Retribusi Service

```typescript
// services/retribusi.ts
import { apiService } from './api';

export class RetribusiService {
  static async getAll(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }) {
    const queryString = new URLSearchParams(params).toString();
    return apiService.get(`/retribusi?${queryString}`);
  }

  static async getById(id: string) {
    return apiService.get(`/retribusi/${id}`);
  }

  static async create(data: CreateRetribusiData) {
    return apiService.post('/retribusi', data);
  }

  static async update(id: string, data: UpdateRetribusiData) {
    return apiService.put(`/retribusi/${id}`, data);
  }

  static async delete(id: string) {
    return apiService.delete(`/retribusi/${id}`);
  }
}
```

## 🔄 Error Handling

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR` (400): Request validation failed
- `UNAUTHORIZED` (401): Authentication required
- `FORBIDDEN` (403): Insufficient permissions
- `NOT_FOUND` (404): Resource not found
- `CONFLICT` (409): Resource already exists
- `RATE_LIMIT` (429): Too many requests
- `INTERNAL_ERROR` (500): Server error

### Error Handling Implementation

```typescript
// utils/errorHandler.ts
export function handleApiError(error: any) {
  if (error.response) {
    const { status, data } = error.response;
    
    switch (status) {
      case 401:
        // Redirect to login
        break;
      case 403:
        // Show permission error
        break;
      case 422:
        // Show validation errors
        break;
      default:
        // Show generic error
        break;
    }
  }
}
```

## 📱 React Native Integration

### Custom Hook untuk API

```typescript
// hooks/useRetribusi.ts
import { useState, useEffect } from 'react';
import { RetribusiService } from '../services/retribusi';

export function useRetribusi() {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchRetribusi = async () => {
    try {
      setLoading(true);
      const response = await RetribusiService.getAll();
      setData(response.data.retribusi);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRetribusi();
  }, []);

  return {
    data,
    loading,
    error,
    refetch: fetchRetribusi,
  };
}
```

---

**API Documentation v1.0** 🚀
