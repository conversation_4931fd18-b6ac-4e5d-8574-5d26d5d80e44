# Supabase Implementation Guide - Report Retribusi

Panduan lengkap implementasi backend menggunakan Supabase untuk aplikasi Report Retribusi.

## 🚀 Setup Supabase Project

### 1. Create Supabase Project

```bash
# 1. <PERSON><PERSON> https://supabase.com dan create account
# 2. Create new project
# 3. Pilih region (Singapore untuk Indonesia)
# 4. Set database password yang kuat
# 5. Wait for project initialization (2-3 menit)
```

### 2. Install Dependencies

```bash
# Install Supabase client
bun add @supabase/supabase-js

# Install additional dependencies untuk auth dan storage
bun add @supabase/auth-helpers-react-native
bun add react-native-url-polyfill
bun add react-native-async-storage
```

### 3. Environment Configuration

```bash
# .env.local
EXPO_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## 🗄️ Database Schema Setup

### 1. Custom Types

```sql
-- Custom enums untuk role dan status
CREATE TYPE user_role AS ENUM ('ADMIN', 'USER', 'SUPERVISOR');
CREATE TYPE deposit_status AS ENUM ('PENDING', 'APPROVED', 'REJECTED');
CREATE TYPE attachment_type AS ENUM ('IMAGE', 'DOCUMENT');
```

### 2. Core Tables

```sql
-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  role user_role DEFAULT 'USER',
  department VARCHAR(100),
  position VARCHAR(100),
  phone VARCHAR(20),
  address TEXT,
  avatar_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES public.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Retribusi categories
CREATE TABLE public.retribusi_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  base_tariff DECIMAL(15,2),
  icon VARCHAR(50),
  color VARCHAR(7),
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES public.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Role retribusi assignments
CREATE TABLE public.role_retribusi_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_role user_role NOT NULL,
  retribusi_category_id UUID REFERENCES public.retribusi_categories(id),
  can_create BOOLEAN DEFAULT true,
  can_edit BOOLEAN DEFAULT false,
  can_delete BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Deposits/Setoran
CREATE TABLE public.deposits (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.users(id) NOT NULL,
  category_id UUID REFERENCES public.retribusi_categories(id),
  title VARCHAR(255) NOT NULL,
  amount DECIMAL(15,2) NOT NULL,
  location VARCHAR(255),
  description TEXT,
  status deposit_status DEFAULT 'PENDING',
  deposit_date DATE NOT NULL,
  approved_by UUID REFERENCES public.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Deposit attachments
CREATE TABLE public.deposit_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  deposit_id UUID REFERENCES public.deposits(id) ON DELETE CASCADE,
  file_name VARCHAR(255) NOT NULL,
  file_path TEXT NOT NULL,
  file_size INTEGER,
  file_type attachment_type DEFAULT 'IMAGE',
  mime_type VARCHAR(100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit logs untuk admin actions
CREATE TABLE public.audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.users(id),
  action VARCHAR(100) NOT NULL,
  table_name VARCHAR(100),
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Row Level Security (RLS) Policies

```sql
-- Enable RLS pada semua tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deposits ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.deposit_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.retribusi_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_retribusi_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON public.users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

CREATE POLICY "Admins can manage users" ON public.users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Deposits policies
CREATE POLICY "Users can view own deposits" ON public.deposits
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create own deposits" ON public.deposits
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own deposits" ON public.deposits
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all deposits" ON public.deposits
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

CREATE POLICY "Admins can manage all deposits" ON public.deposits
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );

-- Deposit attachments policies
CREATE POLICY "Users can view own deposit attachments" ON public.deposit_attachments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.deposits 
      WHERE id = deposit_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Admins can view all attachments" ON public.deposit_attachments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE id = auth.uid() AND role = 'ADMIN'
    )
  );
```

### 4. Database Functions

```sql
-- Function untuk auto-update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers untuk auto-update timestamps
CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON public.users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deposits_updated_at 
  BEFORE UPDATE ON public.deposits 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function untuk audit logging
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.audit_logs (
    user_id, action, table_name, record_id, old_values, new_values
  ) VALUES (
    auth.uid(),
    TG_OP,
    TG_TABLE_NAME,
    COALESCE(NEW.id, OLD.id),
    CASE WHEN TG_OP = 'DELETE' THEN to_jsonb(OLD) ELSE NULL END,
    CASE WHEN TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN to_jsonb(NEW) ELSE NULL END
  );
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Audit triggers
CREATE TRIGGER audit_users_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.users
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER audit_deposits_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.deposits
  FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

## 🔧 Frontend Integration

### 1. Supabase Client Setup

```typescript
// lib/supabase.ts
import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string;
          role: 'ADMIN' | 'USER' | 'SUPERVISOR';
          department: string | null;
          position: string | null;
          phone: string | null;
          address: string | null;
          avatar_url: string | null;
          is_active: boolean;
          created_by: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name: string;
          role?: 'ADMIN' | 'USER' | 'SUPERVISOR';
          department?: string | null;
          position?: string | null;
          phone?: string | null;
          address?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
          created_by?: string | null;
        };
        Update: {
          email?: string;
          full_name?: string;
          role?: 'ADMIN' | 'USER' | 'SUPERVISOR';
          department?: string | null;
          position?: string | null;
          phone?: string | null;
          address?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
        };
      };
      deposits: {
        Row: {
          id: string;
          user_id: string;
          category_id: string | null;
          title: string;
          amount: number;
          location: string | null;
          description: string | null;
          status: 'PENDING' | 'APPROVED' | 'REJECTED';
          deposit_date: string;
          approved_by: string | null;
          approved_at: string | null;
          rejection_reason: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          category_id?: string | null;
          title: string;
          amount: number;
          location?: string | null;
          description?: string | null;
          status?: 'PENDING' | 'APPROVED' | 'REJECTED';
          deposit_date: string;
        };
        Update: {
          category_id?: string | null;
          title?: string;
          amount?: number;
          location?: string | null;
          description?: string | null;
          status?: 'PENDING' | 'APPROVED' | 'REJECTED';
          deposit_date?: string;
          approved_by?: string | null;
          approved_at?: string | null;
          rejection_reason?: string | null;
        };
      };
    };
  };
}
```

### 2. Authentication Service

```typescript
// services/authService.ts
import { supabase } from '../lib/supabase';
import type { Database } from '../lib/supabase';

type User = Database['public']['Tables']['users']['Row'];

export class AuthService {
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    // Get user profile
    const { data: profile } = await supabase
      .from('users')
      .select('*')
      .eq('id', data.user.id)
      .single();

    return { user: data.user, profile };
  }

  static async signUp(userData: {
    email: string;
    password: string;
    full_name: string;
    role?: 'ADMIN' | 'USER';
    department?: string;
    position?: string;
  }) {
    // Only admins can create users
    const { data: currentUser } = await supabase.auth.getUser();
    if (!currentUser.user) throw new Error('Unauthorized');

    const { data: currentProfile } = await supabase
      .from('users')
      .select('role')
      .eq('id', currentUser.user.id)
      .single();

    if (currentProfile?.role !== 'ADMIN') {
      throw new Error('Only admins can create users');
    }

    // Create auth user
    const { data, error } = await supabase.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true,
    });

    if (error) throw error;

    // Create user profile
    const { error: profileError } = await supabase
      .from('users')
      .insert({
        id: data.user.id,
        email: userData.email,
        full_name: userData.full_name,
        role: userData.role || 'USER',
        department: userData.department,
        position: userData.position,
        created_by: currentUser.user.id,
      });

    if (profileError) throw profileError;

    return data;
  }

  static async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  static async getCurrentUser() {
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user) return null;

    const { data: profile } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single();

    return { user, profile };
  }
}
```

## 📱 React Native Integration

### 1. Auth Context

```typescript
// contexts/AuthContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { supabase } from '../lib/supabase';
import { AuthService } from '../services/authService';
import type { User } from '@supabase/supabase-js';
import type { Database } from '../lib/supabase';

type UserProfile = Database['public']['Tables']['users']['Row'];

interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setUser(session?.user ?? null);
      if (session?.user) {
        loadProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);
        if (session?.user) {
          await loadProfile(session.user.id);
        } else {
          setProfile(null);
          setLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const loadProfile = async (userId: string) => {
    try {
      const { data } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();
      
      setProfile(data);
    } catch (error) {
      console.error('Error loading profile:', error);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    const result = await AuthService.signIn(email, password);
    setUser(result.user);
    setProfile(result.profile);
  };

  const signOut = async () => {
    await AuthService.signOut();
    setUser(null);
    setProfile(null);
  };

  const isAdmin = profile?.role === 'ADMIN';

  return (
    <AuthContext.Provider value={{
      user,
      profile,
      loading,
      signIn,
      signOut,
      isAdmin,
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### 2. Deposits Service

```typescript
// services/depositsService.ts
import { supabase } from '../lib/supabase';
import type { Database } from '../lib/supabase';

type Deposit = Database['public']['Tables']['deposits']['Row'];
type DepositInsert = Database['public']['Tables']['deposits']['Insert'];
type DepositUpdate = Database['public']['Tables']['deposits']['Update'];

export class DepositsService {
  static async getDeposits(userId?: string) {
    let query = supabase
      .from('deposits')
      .select(`
        *,
        category:retribusi_categories(*),
        user:users(full_name, department),
        attachments:deposit_attachments(*)
      `)
      .order('created_at', { ascending: false });

    // If userId provided, filter by user (for regular users)
    if (userId) {
      query = query.eq('user_id', userId);
    }

    const { data, error } = await query;
    if (error) throw error;
    return data;
  }

  static async getDepositById(id: string) {
    const { data, error } = await supabase
      .from('deposits')
      .select(`
        *,
        category:retribusi_categories(*),
        user:users(full_name, department, position),
        attachments:deposit_attachments(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  }

  static async createDeposit(deposit: DepositInsert) {
    const { data, error } = await supabase
      .from('deposits')
      .insert(deposit)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateDeposit(id: string, updates: DepositUpdate) {
    const { data, error } = await supabase
      .from('deposits')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteDeposit(id: string) {
    const { error } = await supabase
      .from('deposits')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async approveDeposit(id: string, adminId: string) {
    const { data, error } = await supabase
      .from('deposits')
      .update({
        status: 'APPROVED',
        approved_by: adminId,
        approved_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async rejectDeposit(id: string, reason: string, adminId: string) {
    const { data, error } = await supabase
      .from('deposits')
      .update({
        status: 'REJECTED',
        rejection_reason: reason,
        approved_by: adminId,
        approved_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
}
```

### 3. Storage Service untuk File Upload

```typescript
// services/storageService.ts
import { supabase } from '../lib/supabase';

export class StorageService {
  static async uploadDepositAttachment(
    depositId: string,
    file: {
      uri: string;
      name: string;
      type: string;
    }
  ) {
    try {
      // Convert file to blob for upload
      const response = await fetch(file.uri);
      const blob = await response.blob();

      // Generate unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${depositId}/${Date.now()}.${fileExt}`;

      // Upload to Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('deposit-attachments')
        .upload(fileName, blob, {
          contentType: file.type,
          upsert: false,
        });

      if (uploadError) throw uploadError;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('deposit-attachments')
        .getPublicUrl(fileName);

      // Save attachment record to database
      const { data: attachmentData, error: dbError } = await supabase
        .from('deposit_attachments')
        .insert({
          deposit_id: depositId,
          file_name: file.name,
          file_path: publicUrl,
          file_size: blob.size,
          file_type: file.type.startsWith('image/') ? 'IMAGE' : 'DOCUMENT',
          mime_type: file.type,
        })
        .select()
        .single();

      if (dbError) throw dbError;

      return attachmentData;
    } catch (error) {
      console.error('Upload error:', error);
      throw error;
    }
  }

  static async deleteAttachment(attachmentId: string, filePath: string) {
    // Delete from storage
    const fileName = filePath.split('/').pop();
    if (fileName) {
      await supabase.storage
        .from('deposit-attachments')
        .remove([fileName]);
    }

    // Delete from database
    const { error } = await supabase
      .from('deposit_attachments')
      .delete()
      .eq('id', attachmentId);

    if (error) throw error;
  }
}
```

## 🎯 Next Steps

1. **Setup Supabase Project** - Create account dan project
2. **Run Database Schema** - Execute SQL scripts di SQL Editor
3. **Configure Environment** - Add Supabase credentials ke .env
4. **Install Dependencies** - `bun add @supabase/supabase-js`
5. **Implement Services** - Copy services ke project
6. **Setup Auth Context** - Implement authentication
7. **Test Basic CRUD** - Verify database operations

Siap untuk mulai implementasi? Saya akan bantu step-by-step! 🚀
