import { supabase, isMockMode } from '../lib/supabase';
import type { Deposit, DepositInsert, DepositUpdate, RetribusiCategory } from '../lib/supabase';

export interface DepositWithRelations extends Deposit {
  category?: RetribusiCategory;
  user?: {
    full_name: string;
    department: string | null;
    position: string | null;
  };
  attachments?: Array<{
    id: string;
    file_name: string;
    file_path: string;
    file_type: 'IMAGE' | 'DOCUMENT';
  }>;
}

export class DepositsService {
  /**
   * Get deposits berdasarkan role user
   * User biasa: hanya data sendiri
   * Admin: semua data
   */
  static async getDeposits(userId?: string): Promise<DepositWithRelations[]> {
    try {
      let query = supabase
        .from('deposits')
        .select(`
          *,
          category:retribusi_categories(*),
          user:users(full_name, department, position),
          attachments:deposit_attachments(id, file_name, file_path, file_type)
        `)
        .order('created_at', { ascending: false });

      // Filter berdasarkan user jika bukan admin
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;
      if (error) throw error;
      
      return data || [];
    } catch (error) {
      console.error('Error fetching deposits:', error);
      throw error;
    }
  }

  /**
   * Get deposit by ID
   */
  static async getDepositById(id: string): Promise<DepositWithRelations | null> {
    try {
      const { data, error } = await supabase
        .from('deposits')
        .select(`
          *,
          category:retribusi_categories(*),
          user:users(full_name, department, position),
          attachments:deposit_attachments(*)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching deposit:', error);
      throw error;
    }
  }

  /**
   * Create new deposit
   */
  static async createDeposit(deposit: DepositInsert): Promise<Deposit> {
    try {
      // Mock mode: simulate successful creation
      if (isMockMode || !supabase) {
        const mockDeposit: Deposit = {
          id: `deposit_${Date.now()}`,
          user_id: deposit.user_id,
          category_id: deposit.category_id,
          title: deposit.title,
          amount: deposit.amount,
          location: deposit.location,
          description: deposit.description,
          status: deposit.status || 'PENDING',
          deposit_date: deposit.deposit_date,
          approved_by: null,
          approved_at: null,
          rejection_reason: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        console.log('Mock: Created deposit', mockDeposit);
        return mockDeposit;
      }

      const { data, error } = await supabase
        .from('deposits')
        .insert(deposit)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating deposit:', error);
      throw error;
    }
  }

  /**
   * Update deposit
   */
  static async updateDeposit(id: string, updates: DepositUpdate): Promise<Deposit> {
    try {
      const { data, error } = await supabase
        .from('deposits')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating deposit:', error);
      throw error;
    }
  }

  /**
   * Delete deposit
   */
  static async deleteDeposit(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('deposits')
        .delete()
        .eq('id', id);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting deposit:', error);
      throw error;
    }
  }

  /**
   * Get retribusi categories yang bisa diakses user berdasarkan role
   * TODO: Replace with real Supabase query after database setup
   */
  static async getAvailableCategories(userRole: 'ADMIN' | 'USER' | 'SUPERVISOR'): Promise<RetribusiCategory[]> {
    try {
      // Temporary: Use mock data until Supabase is setup
      const { getCategoriesByRole } = await import('../data/retribusiCategories');
      const mockCategories = getCategoriesByRole(userRole);

      // Convert mock data to match RetribusiCategory type
      return mockCategories.map(cat => ({
        ...cat,
        base_tariff: cat.base_tariff || 0,
      })) as RetribusiCategory[];

      // TODO: Uncomment this when Supabase is setup
      /*
      if (userRole === 'ADMIN') {
        // Admin bisa akses semua kategori
        const { data, error } = await supabase
          .from('retribusi_categories')
          .select('*')
          .eq('is_active', true)
          .order('name');

        if (error) throw error;
        return data || [];
      } else {
        // User biasa hanya kategori yang di-assign ke rolenya
        const { data, error } = await supabase
          .from('retribusi_categories')
          .select(`
            *,
            assignments:role_retribusi_assignments!inner(*)
          `)
          .eq('is_active', true)
          .eq('assignments.user_role', userRole)
          .eq('assignments.can_create', true)
          .order('name');

        if (error) throw error;
        return data || [];
      }
      */
    } catch (error) {
      console.error('Error fetching categories:', error);
      throw error;
    }
  }

  /**
   * Get dashboard statistics
   */
  static async getDashboardStats(userId?: string) {
    try {
      let query = supabase
        .from('deposits')
        .select('amount, status, created_at');

      // Filter by user if not admin
      if (userId) {
        query = query.eq('user_id', userId);
      }

      const { data, error } = await query;
      if (error) throw error;

      const today = new Date().toISOString().split('T')[0];
      const thisMonth = new Date().toISOString().slice(0, 7);

      const stats = {
        totalDeposits: data?.length || 0,
        totalAmount: data?.reduce((sum, d) => sum + d.amount, 0) || 0,
        todayDeposits: data?.filter(d => d.created_at.startsWith(today)).length || 0,
        todayAmount: data?.filter(d => d.created_at.startsWith(today))
          .reduce((sum, d) => sum + d.amount, 0) || 0,
        monthlyAmount: data?.filter(d => d.created_at.startsWith(thisMonth))
          .reduce((sum, d) => sum + d.amount, 0) || 0,
        pendingCount: data?.filter(d => d.status === 'PENDING').length || 0,
        approvedCount: data?.filter(d => d.status === 'APPROVED').length || 0,
        rejectedCount: data?.filter(d => d.status === 'REJECTED').length || 0,
      };

      return stats;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  }

  /**
   * Approve deposit (admin only)
   */
  static async approveDeposit(id: string, adminId: string): Promise<Deposit> {
    try {
      const { data, error } = await supabase
        .from('deposits')
        .update({
          status: 'APPROVED',
          approved_by: adminId,
          approved_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error approving deposit:', error);
      throw error;
    }
  }

  /**
   * Reject deposit (admin only)
   */
  static async rejectDeposit(id: string, reason: string, adminId: string): Promise<Deposit> {
    try {
      const { data, error } = await supabase
        .from('deposits')
        .update({
          status: 'REJECTED',
          rejection_reason: reason,
          approved_by: adminId,
          approved_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error rejecting deposit:', error);
      throw error;
    }
  }
}
