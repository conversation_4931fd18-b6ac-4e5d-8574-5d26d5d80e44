-- =============================================
-- Migration Script: Update Existing Schema
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- 1. Update Users Table
-- =============================================

-- Add NIP column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'nip') THEN
        ALTER TABLE public.users ADD COLUMN nip TEXT UNIQUE;
    END IF;
END $$;

-- Remove department column if it exists (replaced by OPD relationship)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'department') THEN
        ALTER TABLE public.users DROP COLUMN department;
    END IF;
END $$;

-- =============================================
-- 2. Create OPD Table
-- =============================================
CREATE TABLE IF NOT EXISTS public.opd (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    code TEXT UNIQUE,
    description TEXT,
    address TEXT,
    phone TEXT,
    email TEXT,
    head_name TEXT,
    head_nip TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 3. Create Jenis Retribusi Table
-- =============================================
CREATE TABLE IF NOT EXISTS public.jenis_retribusi (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    code TEXT NOT NULL UNIQUE,
    base_account_number TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 4. Create User-OPD Relationship Table
-- =============================================
CREATE TABLE IF NOT EXISTS public.user_opd (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    opd_id UUID REFERENCES public.opd(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, opd_id)
);

-- =============================================
-- 5. Update Retribusi Categories Table
-- =============================================

-- First, let's backup existing data if table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'retribusi_categories') THEN
        -- Create backup table
        CREATE TABLE IF NOT EXISTS retribusi_categories_backup AS 
        SELECT * FROM public.retribusi_categories;
        
        -- Drop existing table
        DROP TABLE public.retribusi_categories CASCADE;
    END IF;
END $$;

-- Create new retribusi_categories table with updated structure
CREATE TABLE public.retribusi_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nomor_rekening TEXT NOT NULL UNIQUE,
    nama_retribusi TEXT NOT NULL,
    opd_id UUID REFERENCES public.opd(id) NOT NULL,
    jenis_retribusi_id UUID REFERENCES public.jenis_retribusi(id) NOT NULL,
    description TEXT,
    base_tariff DECIMAL(15,2),
    icon TEXT,
    color TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 6. Create other tables if they don't exist
-- =============================================

CREATE TABLE IF NOT EXISTS public.deposits (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.retribusi_categories(id),
    title TEXT NOT NULL,
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    location TEXT,
    description TEXT,
    status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED')),
    deposit_date DATE NOT NULL,
    approved_by UUID REFERENCES public.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.deposit_attachments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    deposit_id UUID REFERENCES public.deposits(id) ON DELETE CASCADE NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    file_type TEXT DEFAULT 'IMAGE' CHECK (file_type IN ('IMAGE', 'DOCUMENT')),
    mime_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS public.role_retribusi_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_role TEXT NOT NULL CHECK (user_role IN ('ADMIN', 'USER', 'SUPERVISOR')),
    retribusi_category_id UUID REFERENCES public.retribusi_categories(id) ON DELETE CASCADE,
    can_create BOOLEAN DEFAULT true,
    can_edit BOOLEAN DEFAULT false,
    can_delete BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_role, retribusi_category_id)
);

CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    action TEXT NOT NULL,
    table_name TEXT,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 7. Create Indexes (with IF NOT EXISTS equivalent)
-- =============================================

-- Users indexes
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_nip ON public.users(nip);
CREATE INDEX IF NOT EXISTS idx_users_role ON public.users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON public.users(is_active);

-- OPD indexes
CREATE INDEX IF NOT EXISTS idx_opd_name ON public.opd(name);
CREATE INDEX IF NOT EXISTS idx_opd_code ON public.opd(code);
CREATE INDEX IF NOT EXISTS idx_opd_is_active ON public.opd(is_active);

-- Jenis Retribusi indexes
CREATE INDEX IF NOT EXISTS idx_jenis_retribusi_code ON public.jenis_retribusi(code);
CREATE INDEX IF NOT EXISTS idx_jenis_retribusi_is_active ON public.jenis_retribusi(is_active);

-- User-OPD indexes
CREATE INDEX IF NOT EXISTS idx_user_opd_user_id ON public.user_opd(user_id);
CREATE INDEX IF NOT EXISTS idx_user_opd_opd_id ON public.user_opd(opd_id);

-- Retribusi Categories indexes
CREATE INDEX IF NOT EXISTS idx_retribusi_categories_nomor_rekening ON public.retribusi_categories(nomor_rekening);
CREATE INDEX IF NOT EXISTS idx_retribusi_categories_opd_id ON public.retribusi_categories(opd_id);
CREATE INDEX IF NOT EXISTS idx_retribusi_categories_jenis_id ON public.retribusi_categories(jenis_retribusi_id);
CREATE INDEX IF NOT EXISTS idx_retribusi_categories_is_active ON public.retribusi_categories(is_active);

-- Other indexes
CREATE INDEX IF NOT EXISTS idx_deposits_user_id ON public.deposits(user_id);
CREATE INDEX IF NOT EXISTS idx_deposits_category_id ON public.deposits(category_id);
CREATE INDEX IF NOT EXISTS idx_deposits_status ON public.deposits(status);
CREATE INDEX IF NOT EXISTS idx_deposits_deposit_date ON public.deposits(deposit_date);
CREATE INDEX IF NOT EXISTS idx_deposits_created_at ON public.deposits(created_at);

CREATE INDEX IF NOT EXISTS idx_deposit_attachments_deposit_id ON public.deposit_attachments(deposit_id);

CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_table_name ON public.audit_logs(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON public.audit_logs(created_at);

-- =============================================
-- 8. Create/Update Triggers
-- =============================================

-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop existing triggers if they exist and recreate
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_opd_updated_at ON public.opd;
CREATE TRIGGER update_opd_updated_at BEFORE UPDATE ON public.opd
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_jenis_retribusi_updated_at ON public.jenis_retribusi;
CREATE TRIGGER update_jenis_retribusi_updated_at BEFORE UPDATE ON public.jenis_retribusi
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_retribusi_categories_updated_at ON public.retribusi_categories;
CREATE TRIGGER update_retribusi_categories_updated_at BEFORE UPDATE ON public.retribusi_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_deposits_updated_at ON public.deposits;
CREATE TRIGGER update_deposits_updated_at BEFORE UPDATE ON public.deposits
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
