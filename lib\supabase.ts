import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Get environment variables with fallback for testing
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://mock-project.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'mock-anon-key-for-testing';

// Only create real Supabase client if we have real credentials
const isRealSupabase = supabaseUrl.includes('supabase.co') && !supabaseUrl.includes('mock');

export const supabase = isRealSupabase
  ? createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        storage: AsyncStorage,
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
      },
    })
  : null; // Use null for mock mode

// Mock mode flag
export const isMockMode = !isRealSupabase;

// Database Types
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string;
          role: 'ADMIN' | 'USER' | 'SUPERVISOR';
          department: string | null;
          position: string | null;
          phone: string | null;
          address: string | null;
          avatar_url: string | null;
          is_active: boolean;
          created_by: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name: string;
          role?: 'ADMIN' | 'USER' | 'SUPERVISOR';
          department?: string | null;
          position?: string | null;
          phone?: string | null;
          address?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
          created_by?: string | null;
        };
        Update: {
          email?: string;
          full_name?: string;
          role?: 'ADMIN' | 'USER' | 'SUPERVISOR';
          department?: string | null;
          position?: string | null;
          phone?: string | null;
          address?: string | null;
          avatar_url?: string | null;
          is_active?: boolean;
        };
      };
      retribusi_categories: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          base_tariff: number | null;
          icon: string | null;
          color: string | null;
          is_active: boolean;
          created_by: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          name: string;
          description?: string | null;
          base_tariff?: number | null;
          icon?: string | null;
          color?: string | null;
          is_active?: boolean;
          created_by?: string | null;
        };
        Update: {
          name?: string;
          description?: string | null;
          base_tariff?: number | null;
          icon?: string | null;
          color?: string | null;
          is_active?: boolean;
        };
      };
      deposits: {
        Row: {
          id: string;
          user_id: string;
          category_id: string | null;
          title: string;
          amount: number;
          location: string | null;
          description: string | null;
          status: 'PENDING' | 'APPROVED' | 'REJECTED';
          deposit_date: string;
          approved_by: string | null;
          approved_at: string | null;
          rejection_reason: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          category_id?: string | null;
          title: string;
          amount: number;
          location?: string | null;
          description?: string | null;
          status?: 'PENDING' | 'APPROVED' | 'REJECTED';
          deposit_date: string;
        };
        Update: {
          category_id?: string | null;
          title?: string;
          amount?: number;
          location?: string | null;
          description?: string | null;
          status?: 'PENDING' | 'APPROVED' | 'REJECTED';
          deposit_date?: string;
          approved_by?: string | null;
          approved_at?: string | null;
          rejection_reason?: string | null;
        };
      };
      deposit_attachments: {
        Row: {
          id: string;
          deposit_id: string;
          file_name: string;
          file_path: string;
          file_size: number | null;
          file_type: 'IMAGE' | 'DOCUMENT';
          mime_type: string | null;
          created_at: string;
        };
        Insert: {
          deposit_id: string;
          file_name: string;
          file_path: string;
          file_size?: number | null;
          file_type?: 'IMAGE' | 'DOCUMENT';
          mime_type?: string | null;
        };
        Update: {
          file_name?: string;
          file_path?: string;
          file_size?: number | null;
          file_type?: 'IMAGE' | 'DOCUMENT';
          mime_type?: string | null;
        };
      };
      role_retribusi_assignments: {
        Row: {
          id: string;
          user_role: 'ADMIN' | 'USER' | 'SUPERVISOR';
          retribusi_category_id: string;
          can_create: boolean;
          can_edit: boolean;
          can_delete: boolean;
          created_at: string;
        };
        Insert: {
          user_role: 'ADMIN' | 'USER' | 'SUPERVISOR';
          retribusi_category_id: string;
          can_create?: boolean;
          can_edit?: boolean;
          can_delete?: boolean;
        };
        Update: {
          user_role?: 'ADMIN' | 'USER' | 'SUPERVISOR';
          retribusi_category_id?: string;
          can_create?: boolean;
          can_edit?: boolean;
          can_delete?: boolean;
        };
      };
      audit_logs: {
        Row: {
          id: string;
          user_id: string | null;
          action: string;
          table_name: string | null;
          record_id: string | null;
          old_values: any | null;
          new_values: any | null;
          ip_address: string | null;
          user_agent: string | null;
          created_at: string;
        };
        Insert: {
          user_id?: string | null;
          action: string;
          table_name?: string | null;
          record_id?: string | null;
          old_values?: any | null;
          new_values?: any | null;
          ip_address?: string | null;
          user_agent?: string | null;
        };
        Update: {
          user_id?: string | null;
          action?: string;
          table_name?: string | null;
          record_id?: string | null;
          old_values?: any | null;
          new_values?: any | null;
          ip_address?: string | null;
          user_agent?: string | null;
        };
      };
    };
  };
}

// Helper types
export type User = Database['public']['Tables']['users']['Row'];
export type UserInsert = Database['public']['Tables']['users']['Insert'];
export type UserUpdate = Database['public']['Tables']['users']['Update'];

export type Deposit = Database['public']['Tables']['deposits']['Row'];
export type DepositInsert = Database['public']['Tables']['deposits']['Insert'];
export type DepositUpdate = Database['public']['Tables']['deposits']['Update'];

export type RetribusiCategory = Database['public']['Tables']['retribusi_categories']['Row'];
export type RetribusiCategoryInsert = Database['public']['Tables']['retribusi_categories']['Insert'];
export type RetribusiCategoryUpdate = Database['public']['Tables']['retribusi_categories']['Update'];

export type DepositAttachment = Database['public']['Tables']['deposit_attachments']['Row'];
export type DepositAttachmentInsert = Database['public']['Tables']['deposit_attachments']['Insert'];

export type RoleRetribusiAssignment = Database['public']['Tables']['role_retribusi_assignments']['Row'];
export type AuditLog = Database['public']['Tables']['audit_logs']['Row'];

// Enums
export enum UserRole {
  ADMIN = 'ADMIN',
  USER = 'USER',
  SUPERVISOR = 'SUPERVISOR',
}

export enum DepositStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

export enum AttachmentType {
  IMAGE = 'IMAGE',
  DOCUMENT = 'DOCUMENT',
}
