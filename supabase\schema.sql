-- =============================================
-- Report Retribusi Database Schema
-- =============================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================
-- 1. Users Table (extends auth.users)
-- =============================================
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    nip TEXT UNIQUE, -- Nomor Induk P<PERSON>awai
    role TEXT NOT NULL DEFAULT 'USER' CHECK (role IN ('ADMIN', 'USER', 'SUPERVISOR')),
    position TEXT, -- Jabatan
    phone TEXT,
    address TEXT,
    avatar_url TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 2. OPD (Organisasi Perangkat Daerah) Table
-- =============================================
CREATE TABLE public.opd (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    code TEXT UNIQUE,
    description TEXT,
    address TEXT,
    phone TEXT,
    email TEXT,
    head_name TEXT,
    head_nip TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 3. Jenis Retribusi Table
-- =============================================
CREATE TABLE public.jenis_retribusi (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    code TEXT NOT NULL UNIQUE,
    base_account_number TEXT NOT NULL, -- e.g., "*********" for Jasa Umum
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 4. User-OPD Many-to-Many Relationship
-- =============================================
CREATE TABLE public.user_opd (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    opd_id UUID REFERENCES public.opd(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, opd_id)
);

-- =============================================
-- 5. Updated Retribusi Categories Table
-- =============================================
CREATE TABLE public.retribusi_categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    nomor_rekening TEXT NOT NULL UNIQUE, -- e.g., "*********.001.01"
    nama_retribusi TEXT NOT NULL,
    opd_id UUID REFERENCES public.opd(id) NOT NULL,
    jenis_retribusi_id UUID REFERENCES public.jenis_retribusi(id) NOT NULL,
    description TEXT,
    base_tariff DECIMAL(15,2),
    icon TEXT,
    color TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES public.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 3. Deposits Table
-- =============================================
CREATE TABLE public.deposits (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    category_id UUID REFERENCES public.retribusi_categories(id),
    title TEXT NOT NULL,
    amount DECIMAL(15,2) NOT NULL CHECK (amount > 0),
    location TEXT,
    description TEXT,
    status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED')),
    deposit_date DATE NOT NULL,
    approved_by UUID REFERENCES public.users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    rejection_reason TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 4. Deposit Attachments Table
-- =============================================
CREATE TABLE public.deposit_attachments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    deposit_id UUID REFERENCES public.deposits(id) ON DELETE CASCADE NOT NULL,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    file_type TEXT DEFAULT 'IMAGE' CHECK (file_type IN ('IMAGE', 'DOCUMENT')),
    mime_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- 5. Role Retribusi Assignments Table
-- =============================================
CREATE TABLE public.role_retribusi_assignments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_role TEXT NOT NULL CHECK (user_role IN ('ADMIN', 'USER', 'SUPERVISOR')),
    retribusi_category_id UUID REFERENCES public.retribusi_categories(id) ON DELETE CASCADE,
    can_create BOOLEAN DEFAULT true,
    can_edit BOOLEAN DEFAULT false,
    can_delete BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_role, retribusi_category_id)
);

-- =============================================
-- 6. Audit Logs Table
-- =============================================
CREATE TABLE public.audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id),
    action TEXT NOT NULL,
    table_name TEXT,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- Indexes for Performance
-- =============================================
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_nip ON public.users(nip);
CREATE INDEX idx_users_role ON public.users(role);
CREATE INDEX idx_users_is_active ON public.users(is_active);

CREATE INDEX idx_opd_name ON public.opd(name);
CREATE INDEX idx_opd_code ON public.opd(code);
CREATE INDEX idx_opd_is_active ON public.opd(is_active);

CREATE INDEX idx_jenis_retribusi_code ON public.jenis_retribusi(code);
CREATE INDEX idx_jenis_retribusi_is_active ON public.jenis_retribusi(is_active);

CREATE INDEX idx_user_opd_user_id ON public.user_opd(user_id);
CREATE INDEX idx_user_opd_opd_id ON public.user_opd(opd_id);

CREATE INDEX idx_retribusi_categories_nomor_rekening ON public.retribusi_categories(nomor_rekening);
CREATE INDEX idx_retribusi_categories_opd_id ON public.retribusi_categories(opd_id);
CREATE INDEX idx_retribusi_categories_jenis_id ON public.retribusi_categories(jenis_retribusi_id);
CREATE INDEX idx_retribusi_categories_is_active ON public.retribusi_categories(is_active);

CREATE INDEX idx_deposits_user_id ON public.deposits(user_id);
CREATE INDEX idx_deposits_category_id ON public.deposits(category_id);
CREATE INDEX idx_deposits_status ON public.deposits(status);
CREATE INDEX idx_deposits_deposit_date ON public.deposits(deposit_date);
CREATE INDEX idx_deposits_created_at ON public.deposits(created_at);

CREATE INDEX idx_deposit_attachments_deposit_id ON public.deposit_attachments(deposit_id);

CREATE INDEX idx_audit_logs_user_id ON public.audit_logs(user_id);
CREATE INDEX idx_audit_logs_table_name ON public.audit_logs(table_name);
CREATE INDEX idx_audit_logs_created_at ON public.audit_logs(created_at);

-- =============================================
-- Updated At Triggers
-- =============================================
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_opd_updated_at BEFORE UPDATE ON public.opd
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_jenis_retribusi_updated_at BEFORE UPDATE ON public.jenis_retribusi
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_retribusi_categories_updated_at BEFORE UPDATE ON public.retribusi_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deposits_updated_at BEFORE UPDATE ON public.deposits
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
